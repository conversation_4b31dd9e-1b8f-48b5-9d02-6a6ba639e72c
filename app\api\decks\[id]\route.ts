import { NextRequest, NextResponse } from 'next/server';

import { auth0 } from '@/lib/auth0';
import pool, { createTablesIfNotExist } from '@/lib/db';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    const { id: deckId } = await params;

    // Try to get authenticated user (may be null for public decks)
    let userId: string | null = null;
    try {
      const session = await auth0.getSession(request);
      userId = session?.user?.sub || null;
    } catch {
      // Authentication failed - user is not logged in
      // This is OK for public decks
    }

    // First, check if the deck exists and get its public status
    const deckCheckResult = await pool.query(
      `SELECT
        d.id,
        d.user_id,
        d.is_public,
        d.name,
        d.description,
        d.columns,
        d.front_columns,
        d.back_columns,
        d.has_html,
        d.created_at,
        COUNT(dc.id) as card_count
      FROM decks d
      LEFT JOIN deck_cards dc ON d.id = dc.deck_id
      WHERE d.id = $1
      GROUP BY d.id, d.user_id, d.is_public, d.name, d.description, d.columns, d.front_columns, d.back_columns, d.has_html, d.created_at`,
      [deckId],
    );

    if (deckCheckResult.rows.length === 0) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    const deckData = deckCheckResult.rows[0];

    // Check access permissions
    if (!deckData.is_public && (!userId || deckData.user_id !== userId)) {
      // Private deck and user is not the owner
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get deck cards
    const cardsResult = await pool.query(
      'SELECT id, data FROM deck_cards WHERE deck_id = $1 ORDER BY id',
      [deckId],
    );

    const cards = cardsResult.rows.map((row) => ({
      id: row.id,
      data: row.data,
    }));

    // Determine if current user is the owner
    const isOwner = userId === deckData.user_id;

    return NextResponse.json({
      deck: {
        id: deckData.id,
        name: deckData.name,
        description: deckData.description,
        columns: deckData.columns,
        frontColumns: deckData.front_columns,
        backColumns: deckData.back_columns,
        hasHtml: deckData.has_html,
        isPublic: deckData.is_public,
        isOwner,
        cardCount: parseInt(deckData.card_count),
        createdAt: deckData.created_at,
        cards,
      },
    });
  } catch (error) {
    console.error('Error fetching deck:', error);
    return NextResponse.json({ error: 'Failed to fetch deck' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;
    const { id: deckId } = await params;
    const body = await request.json();

    // Validate request
    if (typeof body.isPublic !== 'boolean') {
      return NextResponse.json({ error: 'isPublic must be a boolean' }, { status: 400 });
    }

    // Verify deck ownership
    const deckCheck = await pool.query(
      'SELECT id, is_public FROM decks WHERE id = $1 AND user_id = $2',
      [deckId, userId],
    );

    if (deckCheck.rows.length === 0) {
      return NextResponse.json({ error: 'Deck not found or access denied' }, { status: 404 });
    }

    // Update deck
    await pool.query('UPDATE decks SET is_public = $1, updated_at = NOW() WHERE id = $2', [
      body.isPublic,
      deckId,
    ]);

    return NextResponse.json({
      success: true,
      isPublic: body.isPublic,
      shareUrl: body.isPublic ? `${request.nextUrl.origin}/study?deck=${deckId}` : null,
    });
  } catch (error) {
    console.error('Error updating deck:', error);
    return NextResponse.json({ error: 'Failed to update deck' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;
    const { id: deckId } = await params;

    // Verify deck ownership
    const deckCheck = await pool.query('SELECT id FROM decks WHERE id = $1 AND user_id = $2', [
      deckId,
      userId,
    ]);

    if (deckCheck.rows.length === 0) {
      return NextResponse.json({ error: 'Deck not found or access denied' }, { status: 404 });
    }

    // Delete deck cards first (due to foreign key constraint)
    await pool.query('DELETE FROM deck_cards WHERE deck_id = $1', [deckId]);

    // Delete the deck
    await pool.query('DELETE FROM decks WHERE id = $1 AND user_id = $2', [deckId, userId]);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting deck:', error);
    return NextResponse.json({ error: 'Failed to delete deck' }, { status: 500 });
  }
}
