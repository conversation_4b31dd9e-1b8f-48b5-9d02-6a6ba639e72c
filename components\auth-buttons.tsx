'use client';

import { useUser } from '@auth0/nextjs-auth0';
import { User, LogOut } from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function AuthButtons() {
  const { user, isLoading } = useUser();

  if (isLoading) {
    return (
      <Avatar
        className="h-10 w-10 cursor-pointer rounded-full border-2 border-transparent shadow-sm"
        style={{
          background:
            'linear-gradient(var(--background), var(--background)) padding-box, linear-gradient(to right, #3b82f6, #9333ea) border-box',
        }}
      >
        <AvatarImage src="/placeholder-user.jpg" alt="Loading user" />
        <AvatarFallback className="text-white font-semibold"></AvatarFallback>
      </Avatar>
    );
  }

  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Avatar
            className="h-10 w-10 cursor-pointer rounded-full border-2 border-transparent shadow-sm transition-all duration-300 hover:shadow-md"
            style={{
              background:
                'linear-gradient(var(--background), var(--background)) padding-box, linear-gradient(to right, #3b82f6, #9333ea) border-box',
            }}
          >
            <AvatarImage src={user.picture} alt={`${user.name || 'User'}'s profile`} />
            <AvatarFallback className="text-foreground font-semibold">
              {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="animate-in fade-in-0 zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 min-w-56 bg-gradient-to-br from-background to-background/95 backdrop-blur-lg border border-slate-200 dark:border-slate-800 rounded-xl shadow-lg overflow-hidden"
        >
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-50"></div>
          <div className="relative z-10">
            <DropdownMenuLabel className="px-4 py-3">
              <div className="flex flex-col space-y-1">
                <span className="text-sm font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {user.name}
                </span>
                {user.email && (
                  <span className="text-xs text-slate-600 dark:text-slate-400">{user.email}</span>
                )}
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-800" />
            <DropdownMenuItem
              asChild
              className="cursor-pointer transition-colors hover:bg-blue-100 focus:bg-blue-100 dark:hover:bg-blue-900/20 dark:focus:bg-blue-900/20 rounded-md mx-1 my-1"
            >
              <a
                href="/profile"
                aria-label="Go to your profile page"
                className="flex items-center gap-3 px-3 py-2"
              >
                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                  <User className="text-blue-600 dark:text-blue-400 h-4 w-4" aria-hidden="true" />
                </div>
                <span className="font-medium text-foreground">Profile</span>
              </a>
            </DropdownMenuItem>
            <DropdownMenuItem
              asChild
              className="cursor-pointer transition-colors hover:bg-red-100 focus:bg-red-100 dark:hover:bg-red-900/20 dark:focus:bg-red-900/20 rounded-md mx-1 my-1"
            >
              <a
                href="/auth/logout"
                aria-label="Logout from your account"
                className="flex items-center gap-3 px-3 py-2"
              >
                <div className="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                  <LogOut className="text-red-600 dark:text-red-400 h-4 w-4" aria-hidden="true" />
                </div>
                <span className="font-medium text-foreground">Logout</span>
              </a>
            </DropdownMenuItem>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className="flex gap-2">
      <Button
        variant="ghost"
        asChild
        className="rounded-full bg-background !text-foreground font-medium shadow-md hover:shadow-lg transition-all duration-300 border-2 border-transparent hover:border-black dark:hover:border-white"
        style={{
          background:
            'linear-gradient(var(--background), var(--background)) padding-box, linear-gradient(to right, #3b82f6, #9333ea) border-box',
        }}
      >
        <a href="/auth/login">Login</a>
      </Button>
    </div>
  );
}
