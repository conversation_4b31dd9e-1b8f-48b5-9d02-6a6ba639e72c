import { Auth0Provider } from '@auth0/nextjs-auth0';
import { Analytics } from '@vercel/analytics/next';
import { GeistMono } from 'geist/font/mono';
import { GeistSans } from 'geist/font/sans';
import type { Metadata } from 'next';
import { Noto_Sans, Londrina_Sketch } from 'next/font/google';
import React from 'react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { ClientLayoutContent } from '@/components/client-layout-content';
import { Footer } from '@/components/footer';
import { ProgressProvider } from '@/components/progress-provider';
import { ThemeProvider } from '@/components/theme-provider';
import './globals.css';

const notoSans = Noto_Sans({
  subsets: ['latin'],
  variable: '--font-noto-sans',
  display: 'swap',
});

const londrinaSketch = Londrina_Sketch({
  weight: '400',
  subsets: ['latin'],
  variable: '--font-londrina-sketch',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Amasugi - Interactive Flashcards',
  description: 'Learn with interactive flashcards - upload TSV files and study efficiently',
  generator: 'v0.app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`font-sans ${GeistSans.variable} ${GeistMono.variable} ${notoSans.variable} ${londrinaSketch.variable}`}
      >
        <div className="flex min-h-screen flex-col">
          <Auth0Provider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <ProgressProvider>
                <ClientLayoutContent>{children}</ClientLayoutContent>
                <Footer />
              </ProgressProvider>
            </ThemeProvider>
          </Auth0Provider>
        </div>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}
