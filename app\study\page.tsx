'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useUser } from '@auth0/nextjs-auth0';
import Link from 'next/link';

import { CardConfiguration } from '@/components/card-configuration';
import { FlashcardDisplay } from '@/components/flashcard-display';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { Share2, Globe, Lock, Loader2 } from 'lucide-react';

interface CardData {
  id: string;
  data: Record<string, string>;
}

interface Deck {
  id: string;
  name: string;
  description: string;
  columns: string[];
  frontColumns: string[];
  backColumns: string[];
  hasHtml: boolean;
  isPublic?: boolean;
  isOwner?: boolean;
  cardCount: number;
  createdAt: string;
  cards: CardData[];
}

function StudyContent() {
  const searchParams = useSearchParams();
  const deckId = searchParams.get('deck');
  const { user } = useUser();

  const [deck, setDeck] = useState<Deck | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'configure' | 'study'>('configure');
  const [isToggling, setIsToggling] = useState(false);
  const [requiresAuth, setRequiresAuth] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Study configuration state
  const [isRandomized, setIsRandomized] = useState(false);
  const [cardLimit, setCardLimit] = useState<number | null>(null);
  const [selectedColor, setSelectedColor] = useState('blue');

  useEffect(() => {
    const fetchDeck = async () => {
      if (!deckId) {
        setError('No deck ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Use regular deck API - it handles both public and private access
        const response = await fetch(`/api/decks/${deckId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Deck not found');
          }
          throw new Error('Failed to fetch deck');
        }

        const data = await response.json();

        // Check if deck is public
        if (data.deck.isPublic) {
          // Public deck - allow access for everyone
          setDeck(data.deck);
          setRequiresAuth(false);

          // Record view for public deck only if user is not the owner (non-blocking)
          if (!data.deck.isOwner) {
            recordDeckView(deckId);
          }
        } else {
          // Private deck - check if user has permission
          if (response.status === 401) {
            setRequiresAuth(true);
            throw new Error('You need to be logged in to access this deck');
          } else if (response.status === 403) {
            throw new Error('You do not have permission to access this deck');
          }
          // If we get here, user has permission (and is likely the owner)
          setDeck(data.deck);
          setRequiresAuth(false);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDeck();
  }, [deckId]);

  // Record deck view for analytics (non-blocking)
  const recordDeckView = async (deckId: string) => {
    try {
      await fetch('/api/deck-views', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deckId }),
      });
      // We don't need to handle the response, just fire and forget
    } catch (error) {
      // Silently fail - don't interrupt user experience
      console.error('Failed to record deck view:', error);
    }
  };

  const handleConfigurationChange = (randomized: boolean, limit: number | null, color: string) => {
    setIsRandomized(randomized);
    setCardLimit(limit);
    setSelectedColor(color);
  };

  const handleStartStudying = () => {
    setStep('study');
  };

  const handleBackToConfigure = () => {
    setStep('configure');
  };

  const handleBackToProfile = () => {
    // This will be handled by the back button in CardConfiguration
  };

  const handlePublicToggle = async (isPublic: boolean) => {
    if (!deckId || isToggling) return;

    setIsToggling(true);

    try {
      const response = await fetch(`/api/decks/${deckId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPublic }),
      });

      if (!response.ok) {
        throw new Error('Failed to update deck');
      }

      // Update local state
      setDeck((prevDeck) =>
        prevDeck
          ? {
              ...prevDeck,
              isPublic,
            }
          : null,
      );

      toast({
        title: isPublic ? 'Deck is now public' : 'Deck is now private',
        description: isPublic
          ? 'Share link generated successfully'
          : 'Deck is no longer accessible publicly',
      });
    } catch (error) {
      console.error('Error updating deck:', error);
      toast({
        title: 'Error',
        description: 'Failed to update deck visibility',
        variant: 'destructive',
      });
    } finally {
      setIsToggling(false);
    }
  };

  const copyShareLink = async () => {
    if (!deck?.id) return;

    const shareUrl = `${window.location.origin}/study?deck=${deck.id}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: 'Link copied!',
        description: 'Share link has been copied to clipboard',
        duration: 3000,
      });
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast({
        title: 'Error',
        description: 'Failed to copy link to clipboard',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/decks/${deckId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('Failed to delete deck');
      }
      toast({
        title: 'Deck deleted',
        description: 'The deck has been successfully deleted',
      });
      router.push('/profile');
    } catch (error) {
      console.error('Error deleting deck:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete the deck',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (loading) {
    return (
      <main className="bg-background">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          {/* Top Navigation Bar - Loading State */}
          <div className="mb-6 pb-4 border-b">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0 flex-1">
                <Skeleton className="h-5 sm:h-6 w-32 sm:w-48 mb-2" />
                <Skeleton className="h-3 sm:h-4 w-24 sm:w-32" />
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                <Skeleton className="h-5 sm:h-6 w-16 sm:w-20" />
                <Skeleton className="h-4 sm:h-5 w-20 sm:w-24" />
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-32 w-full" />
            </CardContent>
          </Card>
        </div>
      </main>
    );
  }

  if (error || !deck) {
    return (
      <main className="bg-background">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          {/* Top Navigation Bar - Error State */}
          <div className="mb-6 pb-4 border-b">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl font-bold text-muted-foreground">
                  Study Session
                </h1>
                <p className="text-muted-foreground text-xs sm:text-sm mt-1">Unable to load deck</p>
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>{requiresAuth ? 'Authentication Required' : 'Error'}</CardTitle>
              <CardDescription>
                {requiresAuth
                  ? 'You need to be logged in to access this private deck.'
                  : error || 'Deck not found'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {requiresAuth ? (
                <div className="flex gap-2">
                  <Button asChild>
                    <Link
                      href={`/api/auth/login?returnTo=${encodeURIComponent(window.location.pathname + window.location.search)}`}
                    >
                      Log In
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/profile">Back to Profile</Link>
                  </Button>
                </div>
              ) : (
                <Button asChild>
                  <Link href="/profile">Back to Profile</Link>
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-background">
      <div className="container mx-auto max-w-4xl px-4 py-8">
        {/* Top Navigation Bar - Mobile Optimized */}
        <div className="mb-6 pb-4 border-b">
          {/* Mobile: Stack vertically | Desktop: Horizontal layout */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Left section: Deck info */}
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl font-bold truncate pr-2">{deck.name}</h1>
              {deck.description && (
                <p className="text-muted-foreground text-sm md:text-md mt-1 line-clamp-2">
                  {deck.description}
                </p>
              )}
              <p className="text-muted-foreground text-xs mt-1">
                Created {new Date(deck.createdAt).toLocaleDateString()}
              </p>
            </div>

            {/* Right section: Public/Private Toggle (only for deck owners) */}
            {deck.isOwner && (
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                  {isToggling ? (
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  ) : (
                    <Switch
                      checked={deck.isPublic || false}
                      onCheckedChange={handlePublicToggle}
                      disabled={isToggling}
                      aria-label={`Make deck ${deck.isPublic ? 'private' : 'public'}`}
                    />
                  )}
                  <Globe className="h-4 w-4 text-muted-foreground" />
                </div>

                {deck.isPublic && !isToggling && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyShareLink}
                    className="flex items-center gap-1 text-xs"
                  >
                    <Share2 className="h-3 w-3" />
                    Copy Link
                  </Button>
                )}
              </div>
            )}

            {/* Show public indicator for non-owners viewing public decks */}
            {!deck.isOwner && deck.isPublic && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Globe className="h-4 w-4" />
                <span>Public deck</span>
              </div>
            )}
          </div>
        </div>

        {step === 'configure' && (
          <CardConfiguration
            totalCards={deck.cardCount}
            isRandomized={isRandomized}
            cardLimit={cardLimit}
            selectedColor={selectedColor}
            onConfigurationChange={handleConfigurationChange}
            onStartStudying={handleStartStudying}
            onBackToUpload={handleBackToProfile}
            showBackButton={false}
            showDeleteButton={deck.isOwner}
            onDelete={() => setShowDeleteDialog(true)}
            isDeleting={isDeleting}
          />
        )}

        {step === 'study' && (
          <FlashcardDisplay
            data={deck.cards.map((card) => card.data)}
            frontColumns={deck.frontColumns}
            backColumns={deck.backColumns}
            isRandomized={isRandomized}
            cardLimit={cardLimit}
            selectedColor={selectedColor}
            hasHtml={deck.hasHtml}
            deckId={deck.id}
            deckName={deck.name}
            isLoggedIn={!!user}
            onBackToConfigure={handleBackToConfigure}
            onSessionComplete={(result) => {
              // Handle session completion
              console.log('Session completed:', result);
              // Could show a completion modal or redirect
            }}
          />
        )}
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Deck</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this deck? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  );
}
// Wrapper component that conditionally applies authentication
function StudyPageWrapper() {
  return <StudyContent />;
}

function StudyPage() {
  // Authentication is now handled by the component itself, not middleware
  return <StudyPageWrapper />;
}

export default StudyPage;
