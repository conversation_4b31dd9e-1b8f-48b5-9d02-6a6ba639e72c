'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useProgress } from '@/components/progress-provider';
import { CelebrationModal } from '@/components/celebration-modal';
import { GoalSettingModal } from '@/components/goal-setting-modal';
import {
  TrendingUp,
  Calendar,
  Clock,
  Target,
  ChevronDown,
  ChevronUp,
  BookOpen,
  Flame,
  BarChart3,
  ArrowRight,
  Settings,
} from 'lucide-react';
// Using CSS animations instead of Framer Motion for better compatibility

interface Achievement {
  type: string;
  name: string;
  description: string;
}

interface StudyProgressCardProps {
  className?: string;
}

export function StudyProgressCard({ className }: StudyProgressCardProps) {
  const { activeSession, progressData, refreshProgress, isLoading } = useProgress();
  const [error] = useState<string | null>(null);
  const [showCelebration, setShowCelebration] = useState(false);
  const [celebrationAchievements, setCelebrationAchievements] = useState<Achievement[]>([]);
  const [showGoalSetting, setShowGoalSetting] = useState(false);
  const [userGoals, setUserGoals] = useState({
    dailyCards: 50,
    weeklyCards: 300,
    weeklyStreak: 5,
  });
  const [expandedSections, setExpandedSections] = useState({
    recentActivity: false,
    achievements: false,
    insights: false,
  });

  // Listen for new achievements
  useEffect(() => {
    const handleNewAchievements = (event: CustomEvent) => {
      if (event.detail?.achievements && event.detail.achievements.length > 0) {
        setCelebrationAchievements(event.detail.achievements);
        setShowCelebration(true);
        // Refresh progress data to show updated stats
        setTimeout(() => {
          refreshProgress();
        }, 1000);
      }
    };

    window.addEventListener('newAchievements', handleNewAchievements as EventListener);
    return () => {
      window.removeEventListener('newAchievements', handleNewAchievements as EventListener);
    };
  }, [refreshProgress]);

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    }
    if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    }
    return `${secs}s`;
  };

  const getStreakBadgeColors = (streak: number) => {
    if (streak === 0 || streak < 7) {
      return {
        bg: 'bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-950 dark:to-orange-900',
        text: 'text-orange-700 dark:text-orange-300',
        label: 'text-orange-600 dark:text-orange-400',
      };
    }
    if (streak < 30) {
      return {
        bg: 'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-950 dark:to-blue-900',
        text: 'text-blue-700 dark:text-blue-300',
        label: 'text-blue-600 dark:text-blue-400',
      };
    }
    return {
      bg: 'bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-950 dark:to-purple-900',
      text: 'text-purple-700 dark:text-purple-300',
      label: 'text-purple-600 dark:text-purple-400',
    };
  };

  const getMotivationalMessage = (streak: number, totalSessions: number) => {
    if (streak === 0) {
      return totalSessions === 0 ? 'Start today!' : 'Keep the momentum going!';
    }
    if (streak < 7) return 'Great start! Keep it up!';
    if (streak < 30) return "You're on fire!";
    return 'Amazing dedication!';
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-16 w-full" />
          </div>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error || !progressData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Study Progress
          </CardTitle>
          <CardDescription>Your learning statistics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">{error || 'Unable to load progress data'}</p>
            <Button variant="outline" onClick={refreshProgress} disabled={isLoading}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { completedCards, studyStreak, weeklyProgress, recentSessions } = progressData;
  const streakColors = getStreakBadgeColors(studyStreak || 0);

  return (
    <div className={`animate-in fade-in-0 slide-in-from-bottom-5 duration-500 ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1.5">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Study Progress
              </CardTitle>
              <CardDescription>Your learning statistics and achievements</CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowGoalSetting(true)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              Goals
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Active Session Display */}
          {activeSession && (
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4 animate-pulse">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                    Active Study Session
                  </h3>
                </div>
                <Button asChild size="sm" variant="outline">
                  <Link href={`/study?deck=${activeSession.deckId}`}>
                    <ArrowRight className="h-4 w-4 mr-1" />
                    Resume
                  </Link>
                </Button>
              </div>
              <p className="text-blue-700 dark:text-blue-300 text-sm mb-2">
                {activeSession.deckName}
              </p>
              <div className="grid grid-cols-3 gap-3 text-sm">
                <div className="text-center">
                  <div className="text-blue-900 dark:text-blue-100 font-medium">
                    {Math.floor(activeSession.elapsedTime / 3600)}:
                    {Math.floor((activeSession.elapsedTime % 3600) / 60)
                      .toString()
                      .padStart(2, '0')}
                    :{(activeSession.elapsedTime % 60).toString().padStart(2, '0')}
                  </div>
                  <div className="text-blue-600 dark:text-blue-400 text-xs">Time</div>
                </div>
                <div className="text-center">
                  <div className="text-blue-900 dark:text-blue-100 font-medium">
                    {activeSession.currentCard + 1}/{activeSession.totalCards}
                  </div>
                  <div className="text-blue-600 dark:text-blue-400 text-xs">Progress</div>
                </div>
                <div className="text-center">
                  <div className="text-blue-900 dark:text-blue-100 font-medium">
                    {Math.round((activeSession.currentCard / activeSession.totalCards) * 100)}%
                  </div>
                  <div className="text-blue-600 dark:text-blue-400 text-xs">Complete</div>
                </div>
              </div>
            </div>
          )}

          {/* Main Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-950 dark:to-blue-900 rounded-lg transition-transform hover:scale-105 duration-200">
              <BookOpen className="h-6 w-6 mx-auto mb-2 text-blue-600" />
              <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
                {completedCards?.toLocaleString() || '0'}
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-400">Cards Studied</div>
            </div>

            <div className="text-center p-3 bg-gradient-to-br from-green-100 to-green-200 dark:from-green-950 dark:to-green-900 rounded-lg transition-transform hover:scale-105 duration-200">
              <Target className="h-6 w-6 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                {progressData?.totalSessions || 0}
              </div>
              <div className="text-xs text-green-600 dark:text-green-400">Total Sessions</div>
            </div>

            <div
              className={`text-center p-3 ${streakColors.bg} rounded-lg transition-transform hover:scale-105 duration-200`}
            >
              <Flame className={`h-6 w-6 mx-auto mb-2 ${streakColors.text}`} />
              <div className={`text-2xl font-bold ${streakColors.text}`}>{studyStreak || 0}</div>
              <div className={`text-xs ${streakColors.label}`}>Day Streak</div>
            </div>

            <div className="text-center p-3 bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-950 dark:to-purple-900 rounded-lg transition-transform hover:scale-105 duration-200">
              <Clock className="h-6 w-6 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                {formatDuration(
                  recentSessions?.reduce((total, session) => total + (session.duration || 0), 0) ||
                    0,
                )}
              </div>
              <div className="text-xs text-purple-600 dark:text-purple-400">Total Time</div>
            </div>
          </div>

          {/* Motivational Message */}
          <div className="text-center p-4 bg-gradient-to-r from-indigo-100 to-purple-200 dark:from-indigo-950 dark:to-purple-950 rounded-lg animate-in fade-in-0 duration-500 delay-300">
            <p className="text-indigo-700 dark:text-indigo-300 font-medium animate-pulse">
              {getMotivationalMessage(studyStreak || 0, recentSessions?.length || 0)}
            </p>
          </div>

          {/* Daily Progress Goal - Redesigned */}
          <div className="bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl p-5 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-500 rounded-full blur-md opacity-20 animate-pulse"></div>
                  <Target className="h-6 w-6 text-blue-600 dark:text-blue-400 relative z-10" />
                </div>
                <div>
                  <h3 className="font-semibold text-foreground">Today's Goal</h3>
                  <p className="text-xs text-muted-foreground">Daily card target</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {weeklyProgress?.[weeklyProgress.length - 1] || 0}
                  <span className="text-sm font-normal text-muted-foreground">
                    /{userGoals.dailyCards}
                  </span>
                </div>
                <div className="text-xs text-muted-foreground">cards</div>
              </div>
            </div>
          </div>

          {/* Weekly Progress - Redesigned to match Today's Goal */}
          <div className="bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl p-5 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500 rounded-full blur-md opacity-20 animate-pulse"></div>
                  <Calendar className="h-6 w-6 text-green-600 dark:text-green-400 relative z-10" />
                </div>
                <div>
                  <h3 className="font-semibold text-foreground">Weekly Activity</h3>
                  <p className="text-xs text-muted-foreground">Study sessions this week</p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  {recentSessions?.length || 0}
                  <span className="text-sm font-normal text-muted-foreground">/7 days</span>
                </div>
                <div className="text-xs text-muted-foreground">sessions</div>
              </div>
            </div>

            {/* Enhanced Heatmap with Activity Levels */}
            <div className="flex justify-center mb-3 mt-4">
              <div className="grid grid-cols-7 gap-1">
                {Array.from({ length: 7 }).map((_, index) => {
                  // Calculate the date for each day of the week (starting from Sunday)
                  const today = new Date();
                  const dayDate = new Date(today);
                  dayDate.setDate(today.getDate() - today.getDay() + index);

                  // Get day of month
                  const dayOfMonth = dayDate.getDate();

                  // Format date as YYYY-MM-DD for comparison
                  const formattedDate = dayDate.toISOString().split('T')[0];

                  // Check activity level for this date
                  const sessionsOnDate = recentSessions
                    ? recentSessions.filter((session) => {
                        const sessionDate = new Date(session.date);
                        const formattedSessionDate = sessionDate.toISOString().split('T')[0];
                        return formattedSessionDate === formattedDate;
                      })
                    : [];

                  const activityLevel = sessionsOnDate.length;
                  const totalCards = sessionsOnDate.reduce(
                    (sum, session) => sum + (session.cardsStudied || 0),
                    0,
                  );
                  const totalTime = sessionsOnDate.reduce(
                    (sum, session) => sum + (session.duration || 0),
                    0,
                  );

                  // Determine heatmap color based on activity level
                  const getHeatmapColor = (level: number) => {
                    if (level === 0) return 'bg-gray-100 dark:bg-gray-800';
                    if (level === 1) return 'bg-green-200 dark:bg-green-800';
                    if (level === 2) return 'bg-green-400 dark:bg-green-700';
                    return 'bg-green-600 dark:bg-green-600 text-white';
                  };

                  const getActivitySymbol = (level: number) => {
                    if (level === 0) return '';
                    if (level === 1) return '●';
                    if (level === 2) return '●●';
                    return '●●●';
                  };

                  const isToday = dayDate.toDateString() === today.toDateString();
                  const isFuture = dayDate > today;

                  return (
                    <div key={index} className="text-center group relative">
                      <div className="text-xs text-muted-foreground mb-1">
                        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][index].slice(0, 1)}
                      </div>
                      <div
                        className={`h-8 w-8 rounded-md flex flex-col items-center justify-center text-xs font-medium transition-all duration-300 ${
                          isToday
                            ? 'ring-2 ring-green-500 dark:ring-green-400'
                            : isFuture
                              ? 'opacity-50'
                              : ''
                        } ${getHeatmapColor(activityLevel)} ${
                          activityLevel > 0 ? 'shadow-sm hover:scale-105 cursor-pointer' : ''
                        }`}
                        title={
                          isFuture
                            ? 'Future day'
                            : activityLevel > 0
                              ? `${activityLevel} session(s) on ${dayDate.toLocaleDateString()}\n${totalCards} cards studied\n${formatDuration(totalTime)} total time`
                              : `No activity on ${dayDate.toLocaleDateString()}`
                        }
                      >
                        <span
                          className={`${activityLevel > 0 ? 'text-white' : 'text-muted-foreground'} font-medium text-xs leading-none`}
                        >
                          {dayOfMonth}
                        </span>
                        {activityLevel > 0 && (
                          <span className="text-[6px] leading-none mt-0.5">
                            {getActivitySymbol(activityLevel)}
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Recent Activity Section */}
          <div className="border-t border-border pt-4">
            <Button
              variant="ghost"
              className="w-full justify-between p-3 h-auto rounded-lg hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-950/30 dark:hover:text-blue-300 transition-colors duration-200"
              onClick={() => toggleSection('recentActivity')}
              aria-expanded={expandedSections.recentActivity}
              aria-controls="recent-activity"
            >
              <span className="font-medium flex items-center gap-2 text-foreground">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                Recent Activity
              </span>
              {expandedSections.recentActivity ? (
                <ChevronUp className="h-5 w-5 text-blue-500" />
              ) : (
                <ChevronDown className="h-5 w-5 text-blue-500" />
              )}
            </Button>

            <div
              className={`transition-all duration-300 overflow-hidden ${
                expandedSections.recentActivity
                  ? 'max-h-[1200px] opacity-100 pb-2'
                  : 'max-h-0 opacity-0'
              }`}
            >
              <div id="recent-activity" className="mt-4 space-y-3">
                {recentSessions && recentSessions.length > 0 ? (
                  recentSessions.slice(0, 4).map((session, index) => (
                    <div
                      key={`${session.deckName}-${index}`}
                      className={`flex items-center justify-between p-4 rounded-lg animate-in slide-in-from-left-5 duration-300 border ${
                        index % 2 === 0
                          ? 'bg-gradient-to-r from-blue-50/80 to-indigo-50/80 border-blue-200/50 dark:from-blue-950/30 dark:to-indigo-950/30 dark:border-blue-800/50'
                          : 'bg-gradient-to-r from-purple-50/80 to-pink-50/80 border-purple-200/50 dark:from-purple-950/30 dark:to-pink-950/30 dark:border-purple-800/50'
                      }`}
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex-shrink-0">
                            <div
                              className={`h-8 w-8 rounded-full flex items-center justify-center ${
                                index % 2 === 0
                                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-300'
                                  : 'bg-purple-100 text-purple-600 dark:bg-purple-900/50 dark:text-purple-300'
                              }`}
                            >
                              <BookOpen className="h-4 w-4" />
                            </div>
                          </div>
                          <div className="font-medium truncate text-foreground">
                            {session.deckName}
                          </div>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-foreground/80">
                          <span>{session.date ? session.date.toLocaleDateString() : 'Recent'}</span>
                          <span>{session.cardsStudied || 0} cards</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-sm ml-4">
                        <span className="font-medium text-foreground">
                          {formatDuration(session.duration || 0)}
                        </span>
                        <div
                          className={`h-6 w-6 rounded-full flex items-center justify-center ${
                            index % 2 === 0
                              ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-300'
                              : 'bg-purple-100 text-purple-600 dark:bg-purple-900/50 dark:text-purple-300'
                          }`}
                        >
                          <Clock className="h-3 w-3" />
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
                    <TrendingUp className="h-12 w-12 mx-auto text-blue-400 dark:text-blue-500 mb-2" />
                    <p className="font-medium text-foreground">No recent activity</p>
                    <p className="text-sm mt-1 text-muted-foreground">
                      Start studying to see your progress here!
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Achievements Section */}
          {/* Note: Achievements functionality needs to be implemented in the provider */}
        </CardContent>
      </Card>

      {/* Celebration Modal */}
      <CelebrationModal
        achievements={celebrationAchievements}
        isOpen={showCelebration}
        onClose={() => {
          setShowCelebration(false);
          setCelebrationAchievements([]);
        }}
      />

      {/* Goal Setting Modal */}
      <GoalSettingModal
        isOpen={showGoalSetting}
        onClose={() => setShowGoalSetting(false)}
        onSave={(goals) => {
          // Save goals to state
          setUserGoals(goals);
          // Here you could also save goals to an API
          console.log('Saving goals:', goals);
          // For now, just close the modal
        }}
      />
    </div>
  );
}
