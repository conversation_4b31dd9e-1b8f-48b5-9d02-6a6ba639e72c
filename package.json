{"name": "amas<PERSON>i", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev", "lint-format": "next lint --fix && prettier --write .", "format": "prettier --write ."}, "dependencies": {"@auth0/nextjs-auth0": "^4.9.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@remix-run/react": "latest", "@sveltejs/kit": "latest", "@vercel/analytics": "latest", "@vercel/speed-insights": "^1.2.0", "ai": "^5.0.30", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "geist": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.5.2", "next-themes": "latest", "pg": "^8.16.3", "react": "^19", "react-day-picker": "9.8.0", "react-dom": "^19", "react-hook-form": "^7.60.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.4", "sonner": "^1.7.4", "svelte": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "vue": "latest", "vue-router": "latest", "zod": "3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.34.0", "@tailwindcss/postcss": "^4.1.9", "@types/node": "^22", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "eslint": "^9.17.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-tailwindcss": "^3.18.1", "postcss": "^8.5", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^4.1.9", "tw-animate-css": "1.3.3", "typescript": "^5", "typescript-eslint": "^8.42.0"}}