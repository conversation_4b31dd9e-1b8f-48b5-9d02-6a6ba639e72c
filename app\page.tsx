'use client';
import { PublicDecks } from '@/components/public-decks';

export interface FlashcardData {
  [key: string]: string;
}

export interface AppState {
  step: 'upload' | 'configure' | 'study';
  data: FlashcardData[];
  columns: string[];
  frontColumns: string[];
  backColumns: string[];
  isRandomized: boolean;
  cardLimit: number | null;
  selectedColor: string;
  hasHtml: boolean; // Added hasHtml flag to track HTML content
  deckId: string;
  deckName: string;
}

export default function Home() {
  return (
    <main className="bg-background">
      <div className="container mx-auto max-w-4xl px-4 py-8">
        <div className="mb-4 text-center">
          <h1
            className="text-foreground mb-2 text-5xl font-bold text-balance"
            style={{ fontFamily: 'var(--font-londrina-sketch)' }}
          >
            Amasugi
          </h1>
          <p className="text-muted-foreground text-lg text-pretty">
            Personalized study sessions anytime, anywhere
          </p>
        </div>

        <PublicDecks />
      </div>
    </main>
  );
}
