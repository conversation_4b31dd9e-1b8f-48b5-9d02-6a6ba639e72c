'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

interface SessionConfig {
  deckId: string;
  deckName: string;
  totalCards: number;
  sessionType?: 'study' | 'review' | 'challenge';
}

interface SessionState {
  isActive: boolean;
  startTime: Date | null;
  currentCard: number;
  cardsCompleted: number;
  elapsedTime: number; // in seconds
  sessionId: string | null;
}

interface SessionMetrics {
  averageTimePerCard: number;
  totalCardsStudied: number;
  sessionDuration: number;
  completionRate: number;
}

interface SessionResult {
  sessionId: string;
  deckId: string;
  cardsStudied: number;
  durationSeconds: number;
  achievements?: Array<{
    type: string;
    name: string;
    description: string;
  }>;
}

const SESSION_STORAGE_KEY = 'flashcard-session-state';
const SESSION_CONFIG_KEY = 'flashcard-session-config';

export function useSessionTracker() {
  // Load initial state and config from localStorage
  const loadInitialState = (): { state: SessionState; config: SessionConfig | null } => {
    if (typeof window === 'undefined') {
      return {
        state: {
          isActive: false,
          startTime: null,
          currentCard: 0,
          cardsCompleted: 0,
          elapsedTime: 0,
          sessionId: null,
        },
        config: null,
      };
    }

    try {
      const storedState = localStorage.getItem(SESSION_STORAGE_KEY);
      const storedConfig = localStorage.getItem(SESSION_CONFIG_KEY);

      let state: SessionState = {
        isActive: false,
        startTime: null,
        currentCard: 0,
        cardsCompleted: 0,
        elapsedTime: 0,
        sessionId: null,
      };

      let config: SessionConfig | null = null;

      if (storedState) {
        const parsed = JSON.parse(storedState);
        if (parsed.startTime) {
          parsed.startTime = new Date(parsed.startTime);
        }
        state = parsed;
      }

      if (storedConfig) {
        config = JSON.parse(storedConfig);
      }

      return { state, config };
    } catch (error) {
      console.warn('Failed to load session data from localStorage:', error);
      // Clear corrupted data
      localStorage.removeItem(SESSION_STORAGE_KEY);
      localStorage.removeItem(SESSION_CONFIG_KEY);
      return {
        state: {
          isActive: false,
          startTime: null,
          currentCard: 0,
          cardsCompleted: 0,
          elapsedTime: 0,
          sessionId: null,
        },
        config: null,
      };
    }
  };

  const initialData = loadInitialState();
  const [sessionState, setSessionState] = useState<SessionState>(initialData.state);
  const [sessionConfig, setSessionConfig] = useState<SessionConfig | null>(initialData.config);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<Date | null>(null);

  // Save session data to localStorage
  const saveSessionData = (state: SessionState, config: SessionConfig | null) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(state));
        if (config) {
          localStorage.setItem(SESSION_CONFIG_KEY, JSON.stringify(config));
        }
      } catch (error) {
        console.warn('Failed to save session data to localStorage:', error);
      }
    }
  };

  // Clear session data from localStorage
  const clearSessionData = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(SESSION_STORAGE_KEY);
      localStorage.removeItem(SESSION_CONFIG_KEY);
    }
  };

  // Save state whenever sessionState changes
  useEffect(() => {
    if (sessionState.isActive || sessionState.sessionId) {
      saveSessionData(sessionState, sessionConfig);
    }
  }, [sessionState, sessionConfig]);

  // Update elapsed time every second when session is active
  useEffect(() => {
    if (sessionState.isActive && sessionState.startTime) {
      intervalRef.current = setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now.getTime() - sessionState.startTime!.getTime()) / 1000);
        setSessionState((prev) => ({
          ...prev,
          elapsedTime: elapsed,
        }));
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [sessionState.isActive, sessionState.startTime]);

  const startSession = useCallback((config: SessionConfig) => {
    const now = new Date();
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    setSessionConfig(config);
    const newState = {
      isActive: true,
      startTime: now,
      currentCard: 0,
      cardsCompleted: 0,
      elapsedTime: 0,
      sessionId,
    };
    setSessionState(newState);
    startTimeRef.current = now;
    // Save immediately to localStorage
    saveSessionData(newState, config);
  }, []);

  const updateProgress = useCallback((currentCard: number, cardsCompleted: number) => {
    setSessionState((prev) => ({
      ...prev,
      currentCard,
      cardsCompleted,
    }));
  }, []);

  const endSession = useCallback(async (): Promise<SessionResult | null> => {
    if (!sessionState.isActive || !sessionConfig || !sessionState.sessionId) {
      return null;
    }

    try {
      // Record the session via API
      const response = await fetch('/api/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deckId: sessionConfig.deckId,
          cardsStudied: sessionState.cardsCompleted,
          durationSeconds: sessionState.elapsedTime,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to record session');
      }

      const result = await response.json();

      // Reset session state
      const resetState = {
        isActive: false,
        startTime: null,
        currentCard: 0,
        cardsCompleted: 0,
        elapsedTime: 0,
        sessionId: null,
      };
      setSessionState(resetState);
      setSessionConfig(null);
      startTimeRef.current = null;
      // Clear localStorage
      clearSessionData();

      return {
        sessionId: sessionState.sessionId,
        deckId: sessionConfig.deckId,
        cardsStudied: sessionState.cardsCompleted,
        durationSeconds: sessionState.elapsedTime,
        achievements: result.achievements,
      };
    } catch (error) {
      console.error('Error ending session:', error);
      return null;
    }
  }, [sessionState, sessionConfig]);

  const pauseSession = useCallback(() => {
    setSessionState((prev) => ({
      ...prev,
      isActive: false,
    }));
  }, []);

  const resumeSession = useCallback(() => {
    if (sessionState.startTime) {
      setSessionState((prev) => ({
        ...prev,
        isActive: true,
      }));
    }
  }, [sessionState.startTime]);

  const getSessionMetrics = useCallback((): SessionMetrics => {
    const averageTimePerCard =
      sessionState.cardsCompleted > 0 ? sessionState.elapsedTime / sessionState.cardsCompleted : 0;

    const completionRate = sessionConfig
      ? (sessionState.cardsCompleted / sessionConfig.totalCards) * 100
      : 0;

    return {
      averageTimePerCard,
      totalCardsStudied: sessionState.cardsCompleted,
      sessionDuration: sessionState.elapsedTime,
      completionRate,
    };
  }, [sessionState, sessionConfig]);

  const formatElapsedTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }, []);

  return {
    sessionState,
    sessionConfig,
    startSession,
    endSession,
    updateProgress,
    pauseSession,
    resumeSession,
    getSessionMetrics,
    formatElapsedTime,
    isSessionActive: sessionState.isActive,
    currentProgress: sessionConfig
      ? (sessionState.currentCard / sessionConfig.totalCards) * 100
      : 0,
  };
}
