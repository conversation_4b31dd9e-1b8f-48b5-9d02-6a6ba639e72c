import { NextRequest, NextResponse } from 'next/server';
import { randomUUID } from 'crypto';

import pool, { createTablesIfNotExist } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    const { deckId } = await request.json();

    if (!deckId) {
      return NextResponse.json({ error: 'Deck ID is required' }, { status: 400 });
    }

    // Verify the deck exists and is public
    const deckCheck = await pool.query('SELECT id, is_public FROM decks WHERE id = $1', [deckId]);

    if (deckCheck.rows.length === 0) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    if (!deckCheck.rows[0].is_public) {
      return NextResponse.json({ error: 'Deck is not public' }, { status: 403 });
    }

    // Get client information for rate limiting (basic approach)
    const clientIP =
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Create a simple hash of IP for privacy (basic approach)
    const crypto = await import('crypto');
    const ipHash = crypto.createHash('sha256').update(clientIP).digest('hex').substring(0, 16);

    // Use a simple session identifier (could be improved with proper session management)
    const sessionId = randomUUID().substring(0, 8);

    // Check for recent view from same IP/session to prevent spam
    const recentViewCheck = await pool.query(
      `SELECT id FROM deck_views
       WHERE deck_id = $1
         AND ip_hash = $2
         AND session_id = $3
         AND viewed_at > NOW() - INTERVAL '1 hour'`,
      [deckId, ipHash, sessionId],
    );

    if (recentViewCheck.rows.length > 0) {
      // Return success but don't record duplicate view
      return NextResponse.json({ success: true, recorded: false });
    }

    // Record the view
    const viewId = `view_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    try {
      await pool.query(
        `INSERT INTO deck_views (id, deck_id, ip_hash, user_agent, session_id)
         VALUES ($1, $2, $3, $4, $5)`,
        [viewId, deckId, ipHash, userAgent, sessionId],
      );
    } catch (insertError: unknown) {
      // If there's a unique constraint violation, just return success
      // This handles race conditions where multiple requests try to insert simultaneously
      if (insertError instanceof Error && 'code' in insertError && insertError.code === '23505') {
        // unique_violation
        return NextResponse.json({ success: true, recorded: false });
      }
      throw insertError;
    }

    return NextResponse.json({ success: true, recorded: true });
  } catch (error) {
    console.error('Error recording deck view:', error);
    return NextResponse.json({ error: 'Failed to record view' }, { status: 500 });
  }
}

// GET endpoint to retrieve view count for a deck (for deck owners)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const deckId = searchParams.get('deckId');

    if (!deckId) {
      return NextResponse.json({ error: 'Deck ID is required' }, { status: 400 });
    }

    // Count total views for the deck
    const viewCountResult = await pool.query(
      'SELECT COUNT(*) as view_count FROM deck_views WHERE deck_id = $1',
      [deckId],
    );

    const viewCount = parseInt(viewCountResult.rows[0].view_count);

    return NextResponse.json({ viewCount });
  } catch (error) {
    console.error('Error fetching view count:', error);
    return NextResponse.json({ error: 'Failed to fetch view count' }, { status: 500 });
  }
}
