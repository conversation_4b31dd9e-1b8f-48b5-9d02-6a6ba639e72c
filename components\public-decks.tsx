'use client';

import React, { useState, useEffect, memo } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, BookOpen, Globe, ChevronLeft, ChevronRight } from 'lucide-react';

interface PublicDeck {
  id: string;
  name: string;
  description: string;
  cardCount: number;
  createdAt: string;
  viewCount: number;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export function PublicDecks() {
  const [decks, setDecks] = useState<PublicDeck[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [search, setSearch] = useState('');
  const [sort, setSort] = useState<'view_count' | 'created_at' | 'name' | 'card_count'>(
    'view_count',
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState<PaginationInfo | null>(null);

  const fetchDecks = async (
    page: number = 1,
    searchQuery: string = '',
    sortBy: string = 'view_count',
  ) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        sort: sortBy,
      });

      if (searchQuery.trim()) {
        params.set('search', searchQuery.trim());
      }

      const response = await fetch(`/api/public-decks?${params}`);

      if (!response.ok) {
        throw new Error('Failed to fetch public decks');
      }

      const data = await response.json();
      setDecks(data.decks || []);
      setPagination(data.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDecks(currentPage, search, sort);
  }, [currentPage, sort]);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentPage !== 1) {
        setCurrentPage(1);
      } else {
        fetchDecks(1, search, sort);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [search]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSortChange = (newSort: 'view_count' | 'created_at' | 'name' | 'card_count') => {
    setSort(newSort);
    setCurrentPage(1);
  };

  const handleSearchChange = (newSearch: string) => {
    setSearch(newSearch);
    setCurrentPage(1);
  };

  // Small presentational Deck card (memoized)
  const DeckCard = memo(function DeckCard({ deck }: { deck: PublicDeck }) {
    return (
      <Link
        href={`/study?deck=${deck.id}`}
        className="group rounded-xl border bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 p-5 shadow-sm transition-all duration-300 hover:shadow-md hover:scale-[1.02] focus:shadow-lg focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-500 w-full h-full block animate-in fade-in-0 slide-in-from-bottom-5 duration-500"
        aria-labelledby={`deck-title-${deck.id}`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="absolute inset-0 bg-indigo-500 rounded-full blur-md opacity-20 animate-pulse"></div>
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900 dark:to-indigo-800 flex items-center justify-center relative z-10">
                  <BookOpen className="h-5 w-5 text-indigo-600 dark:text-indigo-300" />
                </div>
              </div>
              <div>
                <h3
                  id={`deck-title-${deck.id}`}
                  className="text-lg font-semibold text-foreground line-clamp-1"
                >
                  {deck.name}
                </h3>
                {deck.description && (
                  <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                    {deck.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mt-2">
            <Badge className="bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 dark:from-emerald-900 dark:to-emerald-800 dark:text-emerald-200 flex items-center gap-1">
              {deck.cardCount} card{deck.cardCount !== 1 ? 's' : ''}
            </Badge>
            <Badge className="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 dark:from-blue-900 dark:to-blue-800 dark:text-blue-200 flex items-center gap-1">
              <Globe className="h-3 w-3" />
              Public
            </Badge>
          </div>

          <div className="mt-4 flex justify-end">
            <div className="flex items-center gap-1 text-sm font-medium text-indigo-600 dark:text-indigo-400 group-hover:text-indigo-700 dark:group-hover:text-indigo-300 transition-colors">
              Study deck
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                className="w-4 h-4 group-hover:translate-x-1 transition-transform"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>
      </Link>
    );
  });

  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const { currentPage, totalPages } = pagination;

    return (
      <div className="flex items-center justify-center gap-2 mt-6">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="flex items-center gap-1 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 hover:bg-gradient-to-br hover:from-slate-100 hover:to-blue-100 dark:hover:from-slate-800 dark:hover:to-slate-700 transition-all duration-300"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>

        <div className="flex items-center gap-1 px-3 py-1 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg">
          <span className="text-sm text-muted-foreground">Page</span>
          <span className="font-medium text-foreground">{currentPage}</span>
          <span className="text-sm text-muted-foreground">of</span>
          <span className="font-medium text-foreground">{totalPages}</span>
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="flex items-center gap-1 bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 hover:bg-gradient-to-br hover:from-slate-100 hover:to-blue-100 dark:hover:from-slate-800 dark:hover:to-slate-700 transition-all duration-300"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  return (
    <div className="animate-in fade-in-0 slide-in-from-bottom-5 duration-500">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1.5">
              <CardTitle className="flex items-center gap-2" id="public-decks-heading">
                <BookOpen className="h-5 w-5" />
                Explore Public Decks
              </CardTitle>
              <CardDescription>
                Discover and study community-created flashcard decks
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Controls: search and sort */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center gap-2 w-full">
              <label htmlFor="deck-search" className="sr-only">
                Search public decks
              </label>
              <div className="relative flex-1 min-w-0">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="deck-search"
                  type="search"
                  value={search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder="Search decks, descriptions..."
                  className="pl-10 border-2 border-gray bg-white dark:bg-background"
                  aria-label="Search public decks"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <label htmlFor="sort" className="sr-only">
                Sort public decks
              </label>
              <Select
                value={sort}
                onValueChange={(value) =>
                  handleSortChange(value as 'view_count' | 'created_at' | 'name' | 'card_count')
                }
              >
                <SelectTrigger
                  className="w-full sm:w-[180px] border-2 border-gray bg-white dark:bg-background cursor-pointer"
                  aria-label="Sort public decks"
                >
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="view_count">Most Popular</SelectItem>
                  <SelectItem value="created_at">Recently Added</SelectItem>
                  <SelectItem value="name">Name: A → Z</SelectItem>
                  <SelectItem value="card_count">Most Cards</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Loading, error, empty, or results */}
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" aria-busy="true">
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={i}
                  className="rounded-xl border bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 p-5 shadow-sm"
                >
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-5 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16 rounded-full" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div
              role="alert"
              aria-live="assertive"
              className="text-destructive p-4 border border-destructive rounded-lg bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30"
            >
              <p className="font-medium">Error: {error}</p>
              <Button
                variant="outline"
                className="mt-2"
                onClick={() => fetchDecks(currentPage, search, sort)}
              >
                Try Again
              </Button>
            </div>
          ) : decks.length === 0 ? (
            <div className="text-center py-8 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
              <BookOpen className="h-12 w-12 mx-auto text-blue-400 dark:text-blue-500 mb-2" />
              <p className="font-medium text-foreground">No public decks found</p>
              <p className="text-sm mt-1 text-muted-foreground">
                {search ? 'Try adjusting your search terms' : 'Check back later for new content'}
              </p>
            </div>
          ) : (
            <>
              <div
                role="list"
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6 auto-rows-fr"
              >
                {decks.map((deck) => (
                  <li key={deck.id} className="list-none">
                    <DeckCard deck={deck} />
                  </li>
                ))}
              </div>

              {renderPagination()}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
