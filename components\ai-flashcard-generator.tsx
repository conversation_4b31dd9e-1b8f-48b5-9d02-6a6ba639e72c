'use client';

import { Bo<PERSON>, AlertCircle } from 'lucide-react';
import React, { useState } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';

interface AIFlashcardGeneratorProps {
  onFileUploaded: (
    data: Array<Record<string, string>>,
    columns: string[],
    hasHtml: boolean,
  ) => void;
}

export function AIFlashcardGenerator({ onFileUploaded }: AIFlashcardGeneratorProps) {
  const [content, setContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerate = async () => {
    if (!content.trim()) {
      setError('Please enter some study material');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/generate-flashcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: content.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate flashcards');
      }

      const data = await response.json();
      onFileUploaded(data.data, data.columns, data.hasHtml);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate flashcards');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card className="border-accent/50 hover:border-accent/80 transition-colors">
        <CardContent className="p-6">
          {isGenerating ? (
            <div className="flex flex-col items-center space-y-4">
              <div className="border-accent h-8 w-8 animate-spin rounded-full border-b-2"></div>
              <div className="text-center">
                <h3 className="mb-2 text-lg font-semibold">Generating Flashcards...</h3>
                <p className="text-muted-foreground">This may take a few seconds</p>
              </div>
            </div>
          ) : (
            <>
              <div className="bg-accent/10 mb-4 rounded-full p-3 w-fit">
                <Bot className="text-accent h-6 w-6" />
              </div>
              <h3 className="mb-2 text-lg font-semibold">Generate Flashcards with AI</h3>
              <p className="text-muted-foreground mb-4 text-pretty">
                Paste your study material below and let AI create flashcards for you
              </p>
              <Textarea
                placeholder="Paste your study material here (notes, articles, etc.)"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={8}
                className="mb-4"
                disabled={isGenerating}
              />
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !content.trim()}
                className="w-full"
              >
                <Bot className="mr-2 h-4 w-4" />
                Generate Flashcards
              </Button>
            </>
          )}
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
