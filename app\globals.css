@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color palette to match flashcard app design brief */
  --background: oklch(1 0 0); /* #ffffff - clean white background */
  --foreground: oklch(0.205 0 0); /* #1f2937 - deep gray for text */
  --card: oklch(0.97 0 0); /* #f1f5f9 - light gray for card backgrounds */
  --card-foreground: oklch(0.205 0 0); /* #1f2937 - text on cards */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.205 0 0);
  --primary: oklch(0.205 0 0); /* #1f2937 - primary elements */
  --primary-foreground: oklch(1 0 0); /* white text on primary */
  --secondary: oklch(0.97 0 0); /* light gray for secondary elements */
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0); /* #4b5563 - muted text */
  --accent: oklch(0.646 0.222 280.116); /* #8b5cf6 - purple accent */
  --accent-foreground: oklch(1 0 0); /* white text on accent */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.85 0 0); /* #d1d5db - darker border for better visibility */
  --input: oklch(0.95 0 0); /* #f3f4f6 - slightly darker input background for better contrast */
  --ring: oklch(0.646 0.222 280.116 / 0.5); /* purple focus ring */
  --chart-1: oklch(0.646 0.222 280.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.5rem; /* 8px radius for modern feel */
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.205 0 0);
  --sidebar-primary: oklch(0.97 0 0);
  --sidebar-primary-foreground: oklch(0.205 0 0);
  --sidebar-accent: oklch(0.646 0.222 280.116);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.646 0.222 280.116 / 0.5);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0); /* darker card background */
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.646 0.222 280.116); /* maintain purple accent in dark mode */
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.646 0.222 280.116 / 0.5);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  /* Updated font configuration for Noto Sans */
  --font-sans: 'Noto Sans', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-londrina-sketch: var(--font-londrina-sketch);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles for better UX */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-muted {
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

.scrollbar-thumb-muted\/50 {
  scrollbar-color: hsl(var(--muted-foreground) / 0.2) transparent;
}

.scrollbar-track-transparent {
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
}

/* Webkit scrollbar styles for better browser support */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thumb-muted::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.scrollbar-thumb-muted\/50::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.2);
  border-radius: 3px;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.5);
}

/* Enhanced card flip transitions - simplified */
.card-transition {
  transition:
    opacity 0.3s ease-out,
    transform 0.3s ease-out;
}

.card-transitioning {
  opacity: 0.7;
  transform: scale(0.98);
}

/* Pulsing border animations for flashcards */
@keyframes pulse-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 197, 253, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(147, 197, 253, 0);
  }
  100% {
    box-shadow: 0 0 rgba(147, 197, 253, 0);
  }
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 rgba(134, 239, 172, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(134, 239, 172, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(134, 239, 172, 0);
  }
}

@keyframes pulse-purple {
  0% {
    box-shadow: 0 0 rgba(196, 181, 253, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(196, 181, 253, 0);
  }
  100% {
    box-shadow: 0 0 rgba(196, 181, 253, 0);
  }
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 0 rgba(252, 165, 165, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(252, 165, 165, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(252, 165, 165, 0);
  }
}

@keyframes pulse-orange {
  0% {
    box-shadow: 0 0 rgba(253, 186, 116, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(253, 186, 116, 0);
  }
  100% {
    box-shadow: 0 0 rgba(253, 186, 116, 0);
  }
}

@keyframes pulse-pink {
  0% {
    box-shadow: 0 0 0 0 rgba(248, 180, 217, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(248, 180, 217, 0);
  }
  100% {
    box-shadow: 0 0 rgba(248, 180, 217, 0);
  }
}

.pulse-border-blue {
  animation: pulse-blue 2s infinite;
}

.pulse-border-green {
  animation: pulse-green 2s infinite;
}

.pulse-border-purple {
  animation: pulse-purple 2s infinite;
}

.pulse-border-red {
  animation: pulse-red 2s infinite;
}

.pulse-border-orange {
  animation: pulse-orange 2s infinite;
}

.pulse-border-pink {
  animation: pulse-pink 2s infinite;
}

/* Enhanced card transition animations for circular navigation */
@keyframes cardSlideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes cardSlideOutLeft {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes cardSlideInLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes cardSlideOutRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Updated card transition classes for smoother animations */
.card-enter-right {
  animation: cardSlideInRight 0.3s ease-out forwards;
}

.card-exit-left {
  animation: cardSlideOutLeft 0.3s ease-out forwards;
}

.card-enter-left {
  animation: cardSlideInLeft 0.3s ease-out forwards;
}

.card-exit-right {
  animation: cardSlideOutRight 0.3s ease-out forwards;
}

/* Enhanced card flip transitions */
.card-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-transitioning {
  opacity: 0.7;
  transform: scale(0.98);
}

/* Smooth progress bar transition */
.progress-transition {
  transition: width 0.3s ease-out;
}

ruby rt {
  margin-bottom: 0.2rem;
}
