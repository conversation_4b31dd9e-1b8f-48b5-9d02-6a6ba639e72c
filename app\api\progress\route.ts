import { NextRequest, NextResponse } from 'next/server';

import { auth0 } from '@/lib/auth0';
import pool from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;

    // Get user progress data
    const progressResult = await pool.query('SELECT * FROM user_progress WHERE user_id = $1', [
      userId,
    ]);

    let progress = null;
    if (progressResult.rows.length > 0) {
      progress = progressResult.rows[0];
    } else {
      // Initialize progress record if it doesn't exist
      try {
        const initResult = await pool.query(
          `INSERT INTO user_progress (id, user_id)
           VALUES (gen_random_uuid()::text, $1)
           RETURNING *`,
          [userId],
        );
        progress = initResult.rows[0];
      } catch (insertError: unknown) {
        // Handle duplicate key violation (race condition)
        if (insertError instanceof Error && 'code' in insertError && insertError.code === '23505') {
          // Re-fetch the existing record
          const existingResult = await pool.query(
            'SELECT * FROM user_progress WHERE user_id = $1',
            [userId],
          );
          if (existingResult.rows.length > 0) {
            progress = existingResult.rows[0];
          }
        } else {
          throw insertError;
        }
      }
    }

    // Get recent study sessions (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const sessionsResult = await pool.query(
      `SELECT
        ss.*,
        d.name as deck_name
       FROM study_sessions ss
       JOIN decks d ON ss.deck_id = d.id
       WHERE ss.user_id = $1 AND ss.created_at >= $2
       ORDER BY ss.created_at DESC
       LIMIT 10`,
      [userId, thirtyDaysAgo.toISOString()],
    );

    // Calculate streak (consecutive days with study sessions)
    const streakResult = await pool.query(
      `SELECT DISTINCT session_date
        FROM study_sessions
        WHERE user_id = $1
        ORDER BY session_date DESC`,
      [userId],
    );

    let currentStreak = 0;
    if (streakResult.rows.length > 0) {
      // Get unique study dates sorted in descending order (most recent first)
      const studyDates = [
        ...new Set(
          streakResult.rows
            .map((row) => row.session_date)
            .filter((date) => typeof date === 'string'),
        ),
      ].sort((a, b) => b.localeCompare(a));

      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];

      // If user hasn't studied today, check if they studied yesterday to continue streak
      let streakStartDate = today;
      if (!studyDates.includes(today)) {
        const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0];
        if (!studyDates.includes(yesterday)) {
          // No streak if they didn't study today or yesterday
          currentStreak = 0;
        } else {
          // Start counting from yesterday
          streakStartDate = yesterday;
        }
      }

      if (studyDates.includes(streakStartDate)) {
        // Count consecutive days backward from the streak start date
        currentStreak = 1; // Count the start date
        const checkDate = new Date(streakStartDate);

        while (true) {
          checkDate.setDate(checkDate.getDate() - 1);
          const dateStr = checkDate.toISOString().split('T')[0];
          if (studyDates.includes(dateStr)) {
            currentStreak++;
          } else {
            break;
          }
        }
      }
    }

    // Get achievements
    const achievementsResult = await pool.query(
      'SELECT * FROM achievements WHERE user_id = $1 ORDER BY unlocked_at DESC',
      [userId],
    );

    // Aggregate session statistics
    const totalSessions = sessionsResult.rows.length;
    const totalCardsStudied = sessionsResult.rows.reduce(
      (sum, session) => sum + session.cards_studied,
      0,
    );
    const totalStudyTime = sessionsResult.rows.reduce(
      (sum, session) => sum + session.duration_seconds,
      0,
    );

    // Calculate weekly progress (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const weeklySessions = await pool.query(
      'SELECT COUNT(*) as session_count, SUM(cards_studied) as cards_count FROM study_sessions WHERE user_id = $1 AND created_at >= $2',
      [userId, sevenDaysAgo.toISOString()],
    );

    const weeklyStats = {
      sessions: parseInt(weeklySessions.rows[0].session_count) || 0,
      cardsStudied: parseInt(weeklySessions.rows[0].cards_count) || 0,
    };

    // Update progress record with current streak
    if (progress) {
      await pool.query(
        `UPDATE user_progress
         SET current_streak_days = $1, updated_at = NOW()
         WHERE user_id = $2`,
        [currentStreak, userId],
      );
    }

    const analytics = {
      progress: {
        totalCardsStudied: progress?.total_cards_studied || 0,
        totalSessions: progress?.total_sessions || 0,
        currentStreakDays: currentStreak,
        longestStreakDays: progress?.longest_streak_days || 0,
        lastStudyDate: progress?.last_study_date,
      },
      recentActivity: {
        sessions: sessionsResult.rows,
        totalCardsStudied,
        totalStudyTime,
        averageSessionTime: totalSessions > 0 ? Math.round(totalStudyTime / totalSessions) : 0,
      },
      weeklyStats,
      achievements: achievementsResult.rows,
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching progress analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch progress analytics' }, { status: 500 });
  }
}
