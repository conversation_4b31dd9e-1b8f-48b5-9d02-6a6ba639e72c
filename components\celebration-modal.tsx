'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trophy, X, Star, Target, Flame, Award } from 'lucide-react';

interface Achievement {
  type: string;
  name: string;
  description: string;
}

interface CelebrationModalProps {
  achievements: Achievement[];
  isOpen: boolean;
  onClose: () => void;
}

export function CelebrationModal({ achievements, isOpen, onClose }: CelebrationModalProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [animationPhase, setAnimationPhase] = useState<'enter' | 'display' | 'exit'>('enter');

  useEffect(() => {
    if (isOpen && achievements.length > 0) {
      setCurrentIndex(0);
      setAnimationPhase('enter');

      // Auto advance through achievements
      const timer = setTimeout(() => {
        setAnimationPhase('display');
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isOpen, achievements]);

  useEffect(() => {
    if (animationPhase === 'display' && achievements.length > 1) {
      const timer = setTimeout(() => {
        if (currentIndex < achievements.length - 1) {
          setCurrentIndex((prev) => prev + 1);
          setAnimationPhase('enter');
        }
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [animationPhase, currentIndex, achievements.length]);

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'first_session':
      case 'ten_sessions':
      case 'fifty_sessions':
        return <Target className="h-8 w-8" />;
      case 'hundred_cards':
      case 'five_hundred_cards':
      case 'thousand_cards':
        return <Star className="h-8 w-8" />;
      case 'streak':
        return <Flame className="h-8 w-8" />;
      default:
        return <Trophy className="h-8 w-8" />;
    }
  };

  const getAchievementColor = (type: string) => {
    switch (type) {
      case 'first_session':
        return 'from-blue-400 to-blue-600';
      case 'ten_sessions':
        return 'from-green-400 to-green-600';
      case 'fifty_sessions':
        return 'from-purple-400 to-purple-600';
      case 'hundred_cards':
        return 'from-yellow-400 to-yellow-600';
      case 'five_hundred_cards':
        return 'from-orange-400 to-orange-600';
      case 'thousand_cards':
        return 'from-red-400 to-red-600';
      default:
        return 'from-indigo-400 to-indigo-600';
    }
  };

  if (!isOpen || achievements.length === 0) return null;

  const currentAchievement = achievements[currentIndex];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      {/* Confetti background effect */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-bounce"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          >
            <div
              className={`w-2 h-2 bg-gradient-to-r ${getAchievementColor(currentAchievement.type)} rounded-full`}
            />
          </div>
        ))}
      </div>

      <Card
        className={`relative max-w-md w-full mx-4 transition-all duration-500 ${
          animationPhase === 'enter' ? 'scale-0 rotate-12' : 'scale-100 rotate-0'
        }`}
      >
        <CardContent className="p-8 text-center">
          <Button variant="ghost" size="sm" className="absolute top-2 right-2" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>

          <div
            className={`mb-6 inline-flex p-4 rounded-full bg-gradient-to-r ${getAchievementColor(currentAchievement.type)} text-white shadow-2xl animate-bounce`}
          >
            {getAchievementIcon(currentAchievement.type)}
          </div>

          <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
            🎉 Achievement Unlocked!
          </h2>

          <h3 className="text-xl font-semibold mb-3 text-foreground">{currentAchievement.name}</h3>

          <p className="text-muted-foreground mb-6">{currentAchievement.description}</p>

          {achievements.length > 1 && (
            <div className="flex justify-center space-x-2 mb-4">
              {achievements.map((_, index) => (
                <div
                  key={index}
                  className={`h-2 w-2 rounded-full transition-all duration-300 ${
                    index === currentIndex ? 'bg-primary' : 'bg-muted'
                  }`}
                />
              ))}
            </div>
          )}

          <Button
            onClick={onClose}
            className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
          >
            <Award className="mr-2 h-4 w-4" />
            Awesome!
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
