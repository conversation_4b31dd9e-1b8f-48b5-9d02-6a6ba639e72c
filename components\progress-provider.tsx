'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import { useUser } from '@auth0/nextjs-auth0';

interface ActiveSession {
  deckId: string;
  deckName: string;
  isActive: boolean;
  startTime: Date;
  currentCard: number;
  totalCards: number;
  elapsedTime: number;
  cardsCompleted: number;
}

interface SessionData {
  deck_name: string;
  created_at: string;
  cards_studied: number;
  duration_seconds: number;
}

interface ProgressData {
  totalDecks: number;
  totalCards: number;
  completedCards: number;
  studyStreak: number;
  lastStudyDate?: Date;
  weeklyProgress?: number[];
  recentSessions?: {
    deckName: string;
    date: Date;
    cardsStudied: number;
    duration: number;
  }[];
  totalSessions?: number;
  weeklyStats?: {
    sessions: number;
    cardsStudied: number;
  };
}

interface ProgressContextValue {
  activeSession: ActiveSession | null;
  setActiveSession: (session: ActiveSession | null) => void;
  refreshProgress: () => Promise<void>;
  progressData: ProgressData | null;
  isLoading: boolean;
}

const ProgressContext = createContext<ProgressContextValue | undefined>(undefined);

export function useProgress() {
  const context = useContext(ProgressContext);
  if (!context) {
    throw new Error('useProgress must be used within a ProgressProvider');
  }
  return context;
}

export function ProgressProvider({ children }: { children: ReactNode }) {
  const [activeSession, setActiveSession] = useState<ActiveSession | null>(null);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { user } = useUser();

  const refreshProgress = useCallback(async () => {
    // Don't fetch progress data if user is not authenticated
    if (!user) {
      setProgressData(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch('/api/progress');
      if (response.ok) {
        const data = await response.json();
        // Map the API response to our ProgressData structure
        const mappedData: ProgressData = {
          totalDecks: data.progress?.totalDecks || 0,
          totalCards: data.progress?.totalCards || 0,
          completedCards: data.progress?.totalCardsStudied || 0,
          studyStreak: data.progress?.currentStreakDays || 0,
          lastStudyDate: data.progress?.lastStudyDate
            ? new Date(data.progress.lastStudyDate)
            : undefined,
          weeklyProgress: [], // This will need to be calculated from weeklyStats or sessions
          recentSessions:
            data.recentActivity?.sessions?.map((session: SessionData) => ({
              deckName: session.deck_name,
              date: new Date(session.created_at),
              cardsStudied: session.cards_studied,
              duration: session.duration_seconds,
            })) || [],
          totalSessions: data.progress?.totalSessions || 0,
          weeklyStats: data.weeklyStats,
        };
        setProgressData(mappedData);
      } else {
        // Handle HTTP errors
        console.error('Failed to fetch progress data:', response.status, response.statusText);
        // Set progressData to null if we can't fetch it
        setProgressData(null);
      }
    } catch (error) {
      console.error('Failed to refresh progress:', error);
      // Set progressData to null if there's an error
      setProgressData(null);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    refreshProgress();
  }, [refreshProgress]);

  const value: ProgressContextValue = {
    activeSession,
    setActiveSession,
    refreshProgress,
    progressData,
    isLoading,
  };

  return <ProgressContext.Provider value={value}>{children}</ProgressContext.Provider>;
}
