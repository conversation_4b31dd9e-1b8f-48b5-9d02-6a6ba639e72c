'use client';

import { Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';

// Alternative compact toggle switch (without dropdown)
export function ThemeToggleSwitch() {
  const [mounted, setMounted] = useState(false);
  const { setTheme, resolvedTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div className="bg-muted h-10 w-10 animate-pulse rounded-full" />;
  }

  const isDark = resolvedTheme === 'dark';

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setTheme(isDark ? 'light' : 'dark')}
      className="group !bg-muted !text-foreground relative h-10 w-10 rounded-full border-2 border-foreground/30 transition-colors hover:border-foreground/50"
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <div className="relative flex h-full w-full items-center justify-center">
        <Sun className="absolute h-4 w-4 scale-100 rotate-0 transition-all duration-300 group-hover:rotate-[360deg] dark:scale-0 dark:-rotate-90" />
        <Moon className="absolute h-4 w-4 scale-0 rotate-90 transition-all duration-300 dark:scale-100 dark:rotate-0 dark:group-hover:rotate-[360deg]" />
      </div>
    </Button>
  );
}
