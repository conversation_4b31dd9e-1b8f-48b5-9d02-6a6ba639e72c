'use client';

import React, { useState, useEffect, useMemo, memo } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, BookOpen, Plus, Globe, Lock, Users } from 'lucide-react';

interface Deck {
  id: string;
  name: string;
  description: string;
  isPublic: boolean;
  cardCount: number;
  createdAt: string;
  viewCount?: number;
}

export function UserDecks() {
  const [decks, setDecks] = useState<Deck[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [search, setSearch] = useState('');
  const [sort, setSort] = useState<'recent' | 'most-cards' | 'az' | 'za'>('recent');

  useEffect(() => {
    const fetchDecks = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/decks');

        if (!response.ok) {
          throw new Error('Failed to fetch decks');
        }

        const data = await response.json();
        setDecks(data.decks || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDecks();
  }, []);

  const filteredDecks = useMemo(() => {
    const q = search.trim().toLowerCase();
    let result = decks.slice();

    if (q) {
      result = result.filter(
        (d) =>
          d.name.toLowerCase().includes(q) ||
          (!!d.description && d.description.toLowerCase().includes(q)),
      );
    }

    switch (sort) {
      case 'recent':
        result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'most-cards':
        result.sort((a, b) => b.cardCount - a.cardCount);
        break;
      case 'az':
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'za':
        result.sort((a, b) => b.name.localeCompare(a.name));
        break;
    }

    return result;
  }, [decks, search, sort]);

  const resultsCount = filteredDecks.length;

  // Small presentational Deck card (memoized)
  const DeckCard = memo(function DeckCard({ deck }: { deck: Deck }) {
    return (
      <Link
        href={`/study?deck=${deck.id}`}
        className="group rounded-xl border bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 p-5 shadow-sm transition-all duration-300 hover:shadow-md hover:scale-[1.02] focus:shadow-lg focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-indigo-500 w-full block animate-in fade-in-0 slide-in-from-bottom-5 duration-500"
        aria-labelledby={`deck-title-${deck.id}`}
      >
        <div className="flex flex-col">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="absolute inset-0 bg-indigo-500 rounded-full blur-md opacity-20 animate-pulse"></div>
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900 dark:to-indigo-800 flex items-center justify-center relative z-10">
                  <BookOpen className="h-5 w-5 text-indigo-600 dark:text-indigo-300" />
                </div>
              </div>
              <div>
                <h3 id={`deck-title-${deck.id}`} className="text-lg font-semibold text-foreground">
                  {deck.name}
                </h3>
                {deck.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                    {deck.description}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 mt-2">
            <Badge className="bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800 dark:from-emerald-900 dark:to-emerald-800 dark:text-emerald-200 flex items-center gap-1">
              {deck.cardCount} card{deck.cardCount !== 1 ? 's' : ''}
            </Badge>
            <Badge
              className={`${
                deck.isPublic
                  ? 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 dark:from-blue-900 dark:to-blue-800 dark:text-blue-200 flex items-center gap-1'
                  : 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 dark:from-gray-900 dark:to-gray-800 dark:text-gray-200 flex items-center gap-1'
              }`}
            >
              {deck.isPublic ? (
                <>
                  <Globe className="h-3 w-3" />
                  Public
                </>
              ) : (
                <>
                  <Lock className="h-3 w-3" />
                  Private
                </>
              )}
            </Badge>
            {deck.isPublic && deck.viewCount !== undefined && (
              <Badge className="bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 dark:from-purple-900 dark:to-purple-800 dark:text-purple-200 flex items-center gap-1">
                <Users className="h-3 w-3" />
                {deck.viewCount} view{deck.viewCount !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>

          <div className="mt-4 flex items-center justify-between">
            <div className="text-xs text-muted-foreground">
              Created: {new Date(deck.createdAt).toLocaleDateString()}
            </div>
            <div className="flex items-center gap-1 text-sm font-medium text-indigo-600 dark:text-indigo-400 group-hover:text-indigo-700 dark:group-hover:text-indigo-300 transition-colors">
              Study deck
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                className="w-4 h-4 group-hover:translate-x-1 transition-transform"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>
      </Link>
    );
  });

  return (
    <div className="animate-in fade-in-0 slide-in-from-bottom-5 duration-500">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-1.5">
              <CardTitle className="flex items-center gap-2" id="your-decks-heading">
                <BookOpen className="h-5 w-5" />
                Your Decks
              </CardTitle>
              <CardDescription>Manage and study your flashcard decks</CardDescription>
            </div>
            <Button asChild size="sm" className="flex items-center gap-2">
              <Link href="/create-deck">
                <Plus className="h-4 w-4" />
                Create new
              </Link>
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Controls: search, sort - only show if user has cards */}
          {decks.some((deck) => deck.cardCount > 0) && (
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div className="flex items-center gap-2 w-full">
                <label htmlFor="deck-search" className="sr-only">
                  Search decks
                </label>
                <div className="relative flex-1 min-w-0">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="deck-search"
                    type="search"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder="Search decks, descriptions..."
                    className="pl-10 border-2 border-gray bg-white dark:bg-background"
                    aria-label="Search decks"
                  />
                </div>
              </div>

              <div className="flex items-center gap-3">
                <label htmlFor="sort" className="sr-only">
                  Sort decks
                </label>
                <Select
                  value={sort}
                  onValueChange={(value) => setSort(value as 'recent' | 'most-cards' | 'az' | 'za')}
                >
                  <SelectTrigger
                    className="w-full sm:w-[180px] border-2 border-gray bg-white dark:bg-background cursor-pointer"
                    aria-label="Sort decks"
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recent">Recently created</SelectItem>
                    <SelectItem value="most-cards">Most cards</SelectItem>
                    <SelectItem value="az">Name: A → Z</SelectItem>
                    <SelectItem value="za">Name: Z → A</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Loading, error, empty, or results */}
          {loading ? (
            <div className="grid grid-cols-1 gap-6" aria-busy="true">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div
                  key={i}
                  className="rounded-xl border bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 p-5 shadow-sm"
                >
                  <div className="flex items-center gap-3 mb-3">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2 flex-1">
                      <Skeleton className="h-5 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16 rounded-full" />
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div
              role="alert"
              aria-live="assertive"
              className="text-destructive p-4 border border-destructive rounded-lg bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30"
            >
              <p className="font-medium">Error: {error}</p>
              <Button variant="outline" className="mt-2" onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          ) : decks.length === 0 ? (
            <div className="text-center py-8 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
              <BookOpen className="h-12 w-12 mx-auto text-blue-400 dark:text-blue-500 mb-2" />
              <p className="font-medium text-foreground">No decks found</p>
              <p className="text-sm mt-1 text-muted-foreground mb-4">
                Create your first deck to get started with flashcards
              </p>
              <Button asChild>
                <Link href="/create-deck">Create Your First Deck</Link>
              </Button>
            </div>
          ) : (
            <>
              <div className="mb-4 text-center text-sm text-muted-foreground" aria-live="polite">
                {search
                  ? `Showing ${resultsCount} result${resultsCount !== 1 ? 's' : ''} for "${search}"`
                  : `Showing all ${resultsCount} deck${resultsCount !== 1 ? 's' : ''}`}
              </div>

              <ul role="list" className="grid grid-cols-1 gap-6">
                {filteredDecks.map((deck) => (
                  <li key={deck.id}>
                    <DeckCard deck={deck} />
                  </li>
                ))}
              </ul>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
