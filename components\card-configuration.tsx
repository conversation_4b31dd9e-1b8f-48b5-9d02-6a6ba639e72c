'use client';

import { Shuffle, <PERSON><PERSON><PERSON><PERSON>, Play, Hash, Infinity, Check, Trash, Loader2 } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';

interface CardConfigurationProps {
  totalCards: number;
  isRandomized: boolean;
  cardLimit: number | null;
  selectedColor: string;
  onConfigurationChange: (
    isRandomized: boolean,
    cardLimit: number | null,
    selectedColor: string,
  ) => void;
  onStartStudying: () => void;
  onBackToUpload: () => void;
  showBackButton?: boolean;
  showDeleteButton?: boolean;
  onDelete?: () => void;
  isDeleting?: boolean;
}

export function CardConfiguration({
  totalCards,
  isRandomized,
  cardLimit,
  selectedColor,
  onConfigurationChange,
  onStartStudying,
  onBackToUpload,
  showBackButton = true,
  showDeleteButton = false,
  onDelete,
  isDeleting = false,
}: CardConfigurationProps) {
  const [selectedPreset, setSelectedPreset] = useState<number | null>(cardLimit);
  const [useLimitToggle, setUseLimitToggle] = useState<boolean>(cardLimit !== null);

  const colorOptions = [
    {
      value: 'blue',
      label: 'Blue',
      bgClass: 'bg-blue-50 dark:bg-blue-950/30',
      borderClass: 'border-blue-300 dark:border-blue-700',
      iconClass: 'text-blue-600 dark:text-blue-400',
      buttonClass: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      value: 'green',
      label: 'Green',
      bgClass: 'bg-green-50 dark:bg-green-950/30',
      borderClass: 'border-green-300 dark:border-green-700',
      iconClass: 'text-green-600 dark:text-green-400',
      buttonClass: 'bg-green-500 hover:bg-green-600',
    },
    {
      value: 'purple',
      label: 'Purple',
      bgClass: 'bg-purple-50 dark:bg-purple-950/30',
      borderClass: 'border-purple-300 dark:border-purple-700',
      iconClass: 'text-purple-600 dark:text-purple-400',
      buttonClass: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      value: 'red',
      label: 'Red',
      bgClass: 'bg-red-50 dark:bg-red-950/30',
      borderClass: 'border-red-300 dark:border-red-700',
      iconClass: 'text-red-600 dark:text-red-400',
      buttonClass: 'bg-red-500 hover:bg-red-600',
    },
    {
      value: 'orange',
      label: 'Orange',
      bgClass: 'bg-orange-50 dark:bg-orange-950/30',
      borderClass: 'border-orange-300 dark:border-orange-700',
      iconClass: 'text-orange-600 dark:text-orange-400',
      buttonClass: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      value: 'pink',
      label: 'Pink',
      bgClass: 'bg-pink-50 dark:bg-pink-950/30',
      borderClass: 'border-pink-300 dark:border-pink-700',
      iconClass: 'text-pink-600 dark:text-pink-400',
      buttonClass: 'bg-pink-500 hover:bg-pink-600',
    },
  ];

  const handleRandomizeChange = (checked: boolean) => {
    onConfigurationChange(checked, cardLimit, selectedColor);
  };

  const handleLimitToggleChange = (checked: boolean) => {
    setUseLimitToggle(checked);
    if (checked) {
      const limit = selectedPreset || Math.ceil(totalCards * 0.5); // Default to 50%
      setSelectedPreset(limit);
      onConfigurationChange(isRandomized, limit, selectedColor);
    } else {
      onConfigurationChange(isRandomized, null, selectedColor);
    }
  };

  const handleSliderChange = (value: number[]) => {
    const limit = value[0];
    setSelectedPreset(limit);
    onConfigurationChange(isRandomized, limit, selectedColor);
  };

  const handleColorChange = (value: string) => {
    onConfigurationChange(isRandomized, cardLimit, value);
  };

  const getPresetPercentage = (limit: number) => {
    return Math.round((limit / totalCards) * 100);
  };

  const effectiveCardCount = cardLimit && cardLimit < totalCards ? cardLimit : totalCards;

  return (
    <Card>
      <CardContent className="space-y-4">
        {/* Back Button at the top - only show if enabled */}
        {showBackButton && (
          <div className="flex justify-start pt-2">
            <Button
              variant="outline"
              onClick={onBackToUpload}
              className="flex items-center gap-2 bg-transparent px-6"
              size="default"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>
        )}

        <div className="from-accent/5 to-accent/10 border-accent/20 flex items-center justify-between rounded-lg border bg-gradient-to-r p-3">
          <div className="flex items-center gap-3">
            <div className="bg-accent h-2 w-2 rounded-full"></div>
            <div>
              <p className="text-sm font-medium">Ready to study</p>
              <p className="text-muted-foreground text-xs">
                {effectiveCardCount} of {totalCards} cards
              </p>
            </div>
          </div>
          <Badge
            variant="outline"
            className="bg-accent/10 text-accent border-accent/30 px-2 py-1 text-xs"
          >
            {effectiveCardCount} cards
          </Badge>
        </div>

        {/* Study Options Section */}
        <div className="space-y-4">
          <div>
            <h3 className="mb-2 text-lg font-semibold">Study Options</h3>
            <p className="text-muted-foreground mb-4 text-sm">
              Customize how you want to study your flashcards
            </p>
          </div>

          <div className="space-y-4">
            {/* Randomization Option */}
            <div
              className={`cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 ${
                isRandomized
                  ? 'border-blue-300 bg-blue-50/50 shadow-sm dark:border-blue-700 dark:bg-blue-950/30'
                  : 'border-border bg-background hover:border-blue-200 dark:hover:border-blue-800'
              }`}
              onClick={() => handleRandomizeChange(!isRandomized)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={`rounded-lg p-2 ${
                      isRandomized
                        ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400'
                        : 'bg-muted text-muted-foreground'
                    }`}
                  >
                    <Shuffle className="h-4 w-4" />
                  </div>
                  <div>
                    <Label className="cursor-pointer text-base font-medium">Randomize Order</Label>
                    <p className="text-muted-foreground mt-1 text-sm">
                      Shuffle cards to study in random order for better retention
                    </p>
                  </div>
                </div>
                <div onClick={(e) => e.stopPropagation()}>
                  <Switch
                    id="randomize"
                    checked={isRandomized}
                    onCheckedChange={handleRandomizeChange}
                    className="peer-focus:ring-blue-300 dark:peer-checked:bg-blue-600 dark:peer-focus:ring-blue-800"
                  />
                </div>
              </div>
            </div>

            {/* Card Limit Option */}
            <div
              className={`cursor-pointer rounded-xl border-2 p-4 transition-all duration-200 ${
                useLimitToggle
                  ? 'border-purple-300 bg-purple-50/50 shadow-sm dark:border-purple-700 dark:bg-purple-950/30'
                  : 'border-border bg-background hover:border-purple-200 dark:hover:border-purple-800'
              }`}
              onClick={() => handleLimitToggleChange(!useLimitToggle)}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div
                      className={`rounded-lg p-2 ${
                        useLimitToggle
                          ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/50 dark:text-purple-400'
                          : 'bg-muted text-muted-foreground'
                      }`}
                    >
                      {useLimitToggle ? (
                        <Hash className="h-4 w-4" />
                      ) : (
                        <Infinity className="h-4 w-4" />
                      )}
                    </div>
                    <div>
                      <Label className="cursor-pointer text-base font-medium">
                        Limit Number of Cards
                      </Label>
                      <p className="text-muted-foreground mt-1 text-sm">
                        {useLimitToggle
                          ? 'Study a specific number of cards from your deck'
                          : 'Study all available cards in your deck'}
                      </p>
                    </div>
                  </div>
                  <div onClick={(e) => e.stopPropagation()}>
                    <Switch
                      id="use-limit"
                      checked={useLimitToggle}
                      onCheckedChange={handleLimitToggleChange}
                      className="peer-checked:bg-purple-600 peer-focus:ring-purple-300 dark:peer-checked:bg-purple-600 dark:peer-focus:ring-purple-800"
                    />
                  </div>
                </div>

                {/* Card Limit Slider */}
                {useLimitToggle && (
                  <div className="space-y-3" onClick={(e) => e.stopPropagation()}>
                    <Label className="text-sm font-medium">Choose card limit:</Label>
                    <div className="space-y-3">
                      <Slider
                        value={[selectedPreset || Math.ceil(totalCards * 0.5)]}
                        onValueChange={handleSliderChange}
                        max={totalCards}
                        min={1}
                        step={1}
                        className="w-full"
                      />
                    </div>
                    {/* Summary section */}
                    <div className="flex h-6 items-center justify-between text-sm">
                      <span className="text-muted-foreground">
                        Selected: {selectedPreset} of {totalCards} cards
                      </span>
                      <Badge
                        variant="outline"
                        className="border-purple-200 bg-purple-50 text-purple-700 dark:border-purple-800 dark:bg-purple-950/30 dark:text-purple-300"
                      >
                        {getPresetPercentage(selectedPreset || 0)}%
                      </Badge>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Card Color Option */}
            <div
              className={`rounded-xl border-2 p-4 transition-all duration-200 ${
                true
                  ? 'border-indigo-300 bg-indigo-50/50 shadow-sm dark:border-indigo-700 dark:bg-indigo-950/30'
                  : 'border-border bg-background hover:border-indigo-200 dark:hover:border-indigo-800'
              }`}
            >
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-indigo-100 p-2 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400">
                      <div className="h-4 w-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    </div>
                    <div>
                      <Label className="text-base font-medium">Card Color Theme</Label>
                      <p className="text-muted-foreground mt-1 text-sm">
                        Choose the color theme for your flashcards
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium">Select color:</Label>
                  <div className="grid grid-cols-3 gap-3">
                    {colorOptions.map((color) => (
                      <button
                        key={color.value}
                        className={`relative h-20 w-full cursor-pointer rounded-lg border-2 shadow-md transition-all duration-200 hover:shadow-lg ${
                          selectedColor === color.value
                            ? `${color.borderClass} shadow-lg`
                            : `${color.borderClass}`
                        } ${color.bgClass} flex flex-col items-center justify-center gap-1`}
                        onClick={() => handleColorChange(color.value)}
                      >
                        <div className="text-foreground text-xs font-medium">{color.label}</div>
                        {selectedColor === color.value && (
                          <div className="absolute top-2 right-2">
                            <Check className={`${color.iconClass} h-3 w-3`} />
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-card flex items-center justify-between rounded-lg border p-3 text-sm">
          <div className="flex items-center gap-4">
            <span className="text-muted-foreground">Cards:</span>
            <span className="font-medium">{effectiveCardCount}</span>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-muted-foreground">Order:</span>
            <span className="font-medium">{isRandomized ? 'Random' : 'Original'}</span>
          </div>
        </div>

        <div className="flex justify-end pt-2">
          <Button
            onClick={onStartStudying}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 px-8 font-semibold text-white shadow-lg transition-all duration-200 hover:from-blue-700 hover:to-purple-700 hover:shadow-xl"
            size="default"
          >
            <Play className="h-5 w-5" />
            Start Studying
          </Button>
        </div>

        {showDeleteButton && onDelete && (
          <div className="mt-6 pt-4 border-t border-destructive/20 dark:border-destructive/30">
            <div className="bg-destructive/5 dark:bg-destructive/10 rounded-lg p-4 border border-destructive/20 dark:border-destructive/30 shadow-sm dark:shadow-md">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="flex items-center gap-3">
                  <div className="rounded-lg bg-destructive/10 dark:bg-destructive/20 p-2">
                    <Trash className="h-4 w-4 text-destructive dark:text-destructive/90" />
                  </div>
                  <div>
                    <h4 className="font-medium text-destructive">Danger Zone</h4>
                    <p className="text-sm text-muted-foreground">
                      Permanently delete this deck and all its cards
                    </p>
                  </div>
                </div>
                <Button
                  variant="destructive"
                  onClick={onDelete}
                  disabled={isDeleting}
                  className="flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                  size="sm"
                >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash className="h-4 w-4" />
                  )}
                  Delete Deck
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
