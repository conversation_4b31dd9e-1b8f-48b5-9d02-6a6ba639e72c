'use client';

import { Upload, FileText, AlertCircle } from 'lucide-react';
import React, { useState, useRef } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface FileUploadProps {
  onFileUploaded: (
    data: Array<Record<string, string>>,
    columns: string[],
    hasHtml: boolean,
  ) => void;
}

export function FileUpload({ onFileUploaded }: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const parseTSV = (
    content: string,
  ): {
    data: Array<Record<string, string>>;
    columns: string[];
    hasHtml: boolean;
  } => {
    const lines = content.trim().split('\n');
    let hasHtml = false;
    let separator = '\t';
    let headers: string[] = [];
    let dataStartIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (line.startsWith('#separator:')) {
        separator = line.split(':')[1] === 'Tab' ? '\t' : line.split(':')[1];
      } else if (line.startsWith('#html:')) {
        hasHtml = line.split(':')[1] === 'true';
      } else if (line.startsWith('#columns:')) {
        headers = line
          .substring(9)
          .split(separator)
          .map((h) => h.trim());
        dataStartIndex = i + 1;
        break;
      } else if (!line.startsWith('#')) {
        // If no metadata, treat first line as headers
        headers = line.split(separator).map((h) => h.trim());
        dataStartIndex = i + 1;
        break;
      }
    }

    if (headers.length < 2) {
      throw new Error('File must contain at least 2 columns');
    }

    if (dataStartIndex >= lines.length) {
      throw new Error('File must contain at least one data row');
    }

    const data = lines.slice(dataStartIndex).map((line, index) => {
      const values = line.split(separator);
      if (values.length !== headers.length) {
        throw new Error(
          `Row ${dataStartIndex + index + 1} has ${values.length} columns, expected ${headers.length}`,
        );
      }

      const row: Record<string, string> = {};
      headers.forEach((header, i) => {
        row[header] = values[i] || '';
      });
      return row;
    });

    return { data, columns: headers, hasHtml };
  };

  const handleFile = async (file: File) => {
    if (!file.name.toLowerCase().endsWith('.tsv')) {
      setError('Please upload a TSV file (.tsv extension)');
      return;
    }

    if (file.size > 3145728) {
      setError('File size must be less than 3MB');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const content = await file.text();
      const { data, columns, hasHtml } = parseTSV(content);
      onFileUploaded(data, columns, hasHtml);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to parse file');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFile(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFile(files[0]);
    }
  };

  return (
    <div className="space-y-4">
      <Card
        className={`cursor-pointer border-2 border-dashed transition-colors ${
          isDragOver
            ? 'border-accent bg-accent/5'
            : 'border-border border-accent/50 hover:border-accent/80'
        }`}
        onDrop={handleDrop}
        onDragOver={(e) => {
          e.preventDefault();
          setIsDragOver(true);
        }}
        onDragLeave={() => setIsDragOver(false)}
        onClick={() => fileInputRef.current?.click()}
      >
        <CardContent className="flex flex-col items-center justify-center px-6 py-12 text-center">
          {isProcessing ? (
            <div className="flex flex-col items-center space-y-2">
              <div className="border-accent h-8 w-8 animate-spin rounded-full border-b-2"></div>
              <p className="text-muted-foreground">Processing file...</p>
            </div>
          ) : (
            <>
              <div className="bg-accent/10 mb-4 rounded-full p-3">
                <Upload className="text-accent h-6 w-6" />
              </div>
              <h3 className="mb-2 text-lg font-semibold">Upload TSV File</h3>
              <p className="text-muted-foreground mb-4 text-pretty">
                Drag and drop your TSV file here, or click to browse
              </p>
              <Button variant="outline" className="mb-2 bg-transparent">
                <FileText className="mr-2 h-4 w-4" />
                Choose File
              </Button>
            </>
          )}
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        accept=".tsv"
        onChange={handleFileSelect}
        className="hidden"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
