'use client';

import { <PERSON><PERSON>Cir<PERSON>, Eye } from 'lucide-react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface ColumnSelectorProps {
  columns: string[];
  frontColumns: string[];
  backColumns: string[];
  data: Array<Record<string, string>>; // Added data prop for preview
  hasHtml: boolean; // Added hasHtml prop
  disabled?: boolean; // Added disabled prop
  onColumnsChange: (frontColumns: string[], backColumns: string[]) => void;
}

export function ColumnSelector({
  columns,
  frontColumns,
  backColumns,
  data, // Accept data prop
  hasHtml, // Accept hasHtml prop
  disabled = false, // Accept disabled prop
  onColumnsChange,
}: ColumnSelectorProps) {
  const handleFrontColumnChange = (column: string, checked: boolean) => {
    let newFrontColumns: string[];
    let newBackColumns = [...backColumns];

    if (checked) {
      newFrontColumns = [...frontColumns, column];
      // Remove from back columns if it was there
      newBackColumns = backColumns.filter((col) => col !== column);
    } else {
      newFrontColumns = frontColumns.filter((col) => col !== column);
    }

    onColumnsChange(newFrontColumns, newBackColumns);
  };

  const handleBackColumnChange = (column: string, checked: boolean) => {
    let newBackColumns: string[];
    let newFrontColumns = [...frontColumns];

    if (checked) {
      newBackColumns = [...backColumns, column];
      // Remove from front columns if it was there
      newFrontColumns = frontColumns.filter((col) => col !== column);
    } else {
      newBackColumns = backColumns.filter((col) => col !== column);
    }

    onColumnsChange(newFrontColumns, newBackColumns);
  };

  const hasValidSelection = frontColumns.length > 0 && backColumns.length > 0;

  return (
    <Card className="from-background to-muted/20 border-0 bg-gradient-to-br shadow-lg">
      <CardHeader className="pb-4">
        <CardTitle className="from-primary to-accent bg-gradient-to-r bg-clip-text text-2xl font-bold text-transparent">
          Configure Card Content
        </CardTitle>
        <CardDescription className="text-base">
          Choose which columns will appear on the front and back of your flashcards
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-8">
          {/* Front Side Section */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="h-3 w-3 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500"></div>
                <h3 className="text-foreground text-xl font-bold">Front Side</h3>
              </div>
              <Badge
                variant="outline"
                className="border-blue-200 bg-gradient-to-r from-blue-50 to-cyan-50 px-3 py-1 font-semibold text-blue-700 dark:border-blue-800 dark:from-blue-950/50 dark:to-cyan-950/50 dark:text-blue-300"
              >
                {frontColumns.length} selected
              </Badge>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              Select columns to display on the front of your flashcards (questions, terms, etc.)
            </p>
            <div className="space-y-3">
              {columns.map((column) => (
                <div
                  key={`front-${column}`}
                  className={`group flex h-[52px] items-center space-x-3 rounded-xl border p-3 transition-all duration-200 ${
                    disabled
                      ? 'bg-muted/20 border-muted cursor-not-allowed'
                      : `cursor-pointer hover:shadow-md ${
                          frontColumns.includes(column)
                            ? 'border-blue-300 bg-gradient-to-r from-blue-50 to-cyan-50 shadow-sm dark:border-blue-700 dark:from-blue-950/40 dark:to-cyan-950/40'
                            : backColumns.includes(column)
                              ? 'bg-muted/30 border-muted cursor-not-allowed'
                              : 'bg-background border-border hover:border-blue-300 hover:bg-blue-50/30 dark:hover:border-blue-700 dark:hover:bg-blue-950/20'
                        }`
                  }`}
                  onClick={() => {
                    if (!disabled && !backColumns.includes(column)) {
                      handleFrontColumnChange(column, !frontColumns.includes(column));
                    }
                  }}
                >
                  <Checkbox
                    id={`front-${column}`}
                    checked={frontColumns.includes(column)}
                    onCheckedChange={(checked) =>
                      handleFrontColumnChange(column, checked as boolean)
                    }
                    disabled={disabled || backColumns.includes(column)}
                    className="pointer-events-none flex-shrink-0 data-[state=checked]:border-blue-600 data-[state=checked]:bg-blue-600"
                  />
                  <Label
                    htmlFor={`front-${column}`}
                    className={`pointer-events-none flex-1 truncate text-sm font-medium ${
                      backColumns.includes(column) ? 'text-muted-foreground' : 'text-foreground'
                    }`}
                  >
                    {column}
                  </Label>
                  <div className="flex h-full w-[84px] flex-shrink-0 items-center justify-end">
                    {frontColumns.includes(column) ? (
                      <Eye className="pointer-events-none h-4 w-4 text-blue-600" />
                    ) : backColumns.includes(column) ? (
                      <Badge
                        variant="secondary"
                        className="bg-muted text-muted-foreground pointer-events-none text-xs"
                      >
                        Used in back
                      </Badge>
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Back Side Section */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="h-3 w-3 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"></div>
                <h3 className="text-foreground text-xl font-bold">Back Side</h3>
              </div>
              <Badge
                variant="outline"
                className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50 px-3 py-1 font-semibold text-purple-700 dark:border-purple-800 dark:from-purple-950/50 dark:to-pink-950/50 dark:text-purple-300"
              >
                {backColumns.length} selected
              </Badge>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              Select columns to display on the back of your flashcards (answers, definitions, etc.)
            </p>
            <div className="space-y-3">
              {columns.map((column) => (
                <div
                  key={`back-${column}`}
                  className={`group flex h-[52px] items-center space-x-3 rounded-xl border p-3 transition-all duration-200 ${
                    disabled
                      ? 'bg-muted/20 border-muted cursor-not-allowed'
                      : `cursor-pointer hover:shadow-md ${
                          backColumns.includes(column)
                            ? 'border-purple-300 bg-gradient-to-r from-purple-50 to-pink-50 shadow-sm dark:border-purple-700 dark:from-purple-950/40 dark:to-pink-950/40'
                            : frontColumns.includes(column)
                              ? 'bg-muted/30 border-muted cursor-not-allowed'
                              : 'bg-background border-border hover:border-purple-300 hover:bg-purple-50/30 dark:hover:border-purple-700 dark:hover:bg-purple-950/20'
                        }`
                  }`}
                  onClick={() => {
                    if (!disabled && !frontColumns.includes(column)) {
                      handleBackColumnChange(column, !backColumns.includes(column));
                    }
                  }}
                >
                  <Checkbox
                    id={`back-${column}`}
                    checked={backColumns.includes(column)}
                    onCheckedChange={(checked) =>
                      handleBackColumnChange(column, checked as boolean)
                    }
                    disabled={disabled || frontColumns.includes(column)}
                    className="pointer-events-none flex-shrink-0 data-[state=checked]:border-purple-600 data-[state=checked]:bg-purple-600"
                  />
                  <Label
                    htmlFor={`back-${column}`}
                    className={`pointer-events-none flex-1 truncate text-sm font-medium ${
                      frontColumns.includes(column) ? 'text-muted-foreground' : 'text-foreground'
                    }`}
                  >
                    {column}
                  </Label>
                  <div className="flex h-full w-[84px] flex-shrink-0 items-center justify-end">
                    {backColumns.includes(column) ? (
                      <Eye className="pointer-events-none h-4 w-4 text-purple-600" />
                    ) : frontColumns.includes(column) ? (
                      <Badge
                        variant="secondary"
                        className="bg-muted text-muted-foreground pointer-events-none text-xs"
                      >
                        Used in front
                      </Badge>
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {!hasValidSelection && (
          <Alert className="border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50 dark:border-amber-800 dark:from-amber-950/30 dark:to-orange-950/30">
            <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
            <AlertDescription className="font-medium text-amber-800 dark:text-amber-200">
              Please select at least one column for both the front and back of your flashcards to
              continue.
            </AlertDescription>
          </Alert>
        )}

        {hasValidSelection && data.length > 0 && (
          <div className="mt-8">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
              <div className="space-y-4">
                <div className="mb-4 flex items-center gap-3 font-semibold text-blue-700 dark:text-blue-300">
                  <div className="h-3 w-3 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                  <span className="text-lg">Front will show:</span>
                </div>
                <div className="space-y-4">
                  {frontColumns.map((col) => (
                    <div
                      key={col}
                      className="bg-background flex min-h-[120px] flex-col rounded-xl border border-blue-200 p-6 shadow-md dark:border-blue-800"
                    >
                      <p className="mb-3 flex-shrink-0 text-sm font-semibold tracking-wide text-blue-600 uppercase dark:text-blue-400">
                        {col}
                      </p>
                      {hasHtml ? (
                        <div
                          className="text-foreground flex-1 overflow-hidden text-base leading-relaxed"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 4,
                            WebkitBoxOrient: 'vertical',
                          }}
                          dangerouslySetInnerHTML={{
                            __html: data[0][col] || '—',
                          }}
                        />
                      ) : (
                        <p
                          className="text-foreground flex-1 overflow-hidden text-base leading-relaxed"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 4,
                            WebkitBoxOrient: 'vertical',
                          }}
                        >
                          {data[0][col] || '—'}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
              <div className="space-y-4">
                <div className="mb-4 flex items-center gap-3 font-semibold text-purple-700 dark:text-purple-300">
                  <div className="h-3 w-3 rounded-full bg-purple-500 dark:bg-purple-400"></div>
                  <span className="text-lg">Back will show:</span>
                </div>
                <div className="space-y-4">
                  {backColumns.map((col) => (
                    <div
                      key={col}
                      className="bg-background flex min-h-[120px] flex-col rounded-xl border border-purple-200 p-6 shadow-md dark:border-purple-800"
                    >
                      <p className="mb-3 flex-shrink-0 text-sm font-semibold tracking-wide text-purple-600 uppercase dark:text-purple-400">
                        {col}
                      </p>
                      {hasHtml ? (
                        <div
                          className="text-foreground flex-1 overflow-hidden text-base leading-relaxed"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 4,
                            WebkitBoxOrient: 'vertical',
                          }}
                          dangerouslySetInnerHTML={{
                            __html: data[0][col] || '—',
                          }}
                        />
                      ) : (
                        <p
                          className="text-foreground flex-1 overflow-hidden text-base leading-relaxed"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 4,
                            WebkitBoxOrient: 'vertical',
                          }}
                        >
                          {data[0][col] || '—'}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
