import { NextRequest, NextResponse } from 'next/server';

import pool, { createTablesIfNotExist } from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ shareId: string }> },
) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    const { shareId } = await params;

    // Get public deck details
    const deckResult = await pool.query(
      `SELECT
        d.id,
        d.name,
        d.description,
        d.columns,
        d.front_columns,
        d.back_columns,
        d.has_html,
        d.created_at,
        COUNT(dc.id) as card_count
      FROM decks d
      LEFT JOIN deck_cards dc ON d.id = dc.deck_id
      WHERE d.share_id = $1 AND d.is_public = true
      GROUP BY d.id, d.name, d.description, d.columns, d.front_columns, d.back_columns, d.has_html, d.created_at`,
      [shareId],
    );

    if (deckResult.rows.length === 0) {
      return NextResponse.json({ error: 'Deck not found or not public' }, { status: 404 });
    }

    const deck = deckResult.rows[0];

    // Get deck cards
    const cardsResult = await pool.query(
      'SELECT id, data FROM deck_cards WHERE deck_id = $1 ORDER BY id',
      [deck.id],
    );

    const cards = cardsResult.rows.map((row) => ({
      id: row.id,
      data: row.data,
    }));

    return NextResponse.json({
      deck: {
        id: deck.id,
        name: deck.name,
        description: deck.description,
        columns: deck.columns,
        frontColumns: deck.front_columns,
        backColumns: deck.back_columns,
        hasHtml: deck.has_html,
        cardCount: parseInt(deck.card_count),
        createdAt: deck.created_at,
        cards,
      },
    });
  } catch (error) {
    console.error('Error fetching shared deck:', error);
    return NextResponse.json({ error: 'Failed to fetch deck' }, { status: 500 });
  }
}
