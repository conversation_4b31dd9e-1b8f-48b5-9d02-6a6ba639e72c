{"tabWidth": 2, "semi": true, "singleQuote": true, "trailingComma": "all", "printWidth": 100, "arrowParens": "always", "bracketSpacing": true, "endOfLine": "lf", "useTabs": false, "quoteProps": "as-needed", "bracketSameLine": false, "overrides": [{"files": ["*.ts", "*.tsx"], "options": {"parser": "typescript"}}, {"files": ["*.json"], "options": {"parser": "json", "trailingComma": "none"}}, {"files": ["*.md", "*.mdx"], "options": {"parser": "markdown", "printWidth": 80}}]}