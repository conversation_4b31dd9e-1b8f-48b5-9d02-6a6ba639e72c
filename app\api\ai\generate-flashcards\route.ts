import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const MAX_CONTENT_LENGTH = 512;

const FlashcardSchema = z.object({
  columns: z.array(z.string()),
  data: z.array(z.record(z.string())),
  hasHtml: z.boolean(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content } = body;

    if (typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json({ error: 'Content must be a non-empty string' }, { status: 400 });
    }

    if (content.length > MAX_CONTENT_LENGTH) {
      return NextResponse.json(
        { error: `Content must be less than ${MAX_CONTENT_LENGTH} characters` },
        { status: 400 },
      );
    }

    const { object } = await generateObject({
      model: 'openai/gpt-5-nano',
      schema: FlashcardSchema,
      system: `You are an expert educational content creator specializing in creating flashcards. Your task is to generate educational flashcard content that is:
- Accurate and factual
- Well-structured with clear columns
- Column names must be consistent
- Balanced in terms of information density per card
`,
      prompt: `Generate data for the following topic: ${content}`,
    });

    console.log('AI Response:', JSON.stringify(object, null, 2)); // Temporary logging for debugging

    return NextResponse.json(object);
  } catch (error) {
    console.error('Error generating flashcards:', error);
    return NextResponse.json({ error: 'Failed to generate flashcards' }, { status: 500 });
  }
}
