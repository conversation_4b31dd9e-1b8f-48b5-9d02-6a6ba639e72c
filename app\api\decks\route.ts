import { NextRequest, NextResponse } from 'next/server';

import { auth0 } from '@/lib/auth0';
import pool, { createTablesIfNotExist } from '@/lib/db';

interface CreateDeckRequest {
  name: string;
  description: string;
  data: Array<Record<string, string>>;
  columns: string[];
  frontColumns: string[];
  backColumns: string[];
  hasHtml: boolean;
}

interface DeckRow {
  id: string;
  name: string;
  description: string;
  is_public: boolean;
  created_at: string;
  card_count: string;
  view_count: string;
}

export async function POST(request: NextRequest) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;
    const body: CreateDeckRequest = await request.json();

    // Validate required fields
    if (!body.name?.trim()) {
      return NextResponse.json({ error: 'Deck name is required' }, { status: 400 });
    }

    if (!body.data || !Array.isArray(body.data) || body.data.length === 0) {
      return NextResponse.json({ error: 'Deck must contain card data' }, { status: 400 });
    }

    if (!body.frontColumns || !Array.isArray(body.frontColumns) || body.frontColumns.length === 0) {
      return NextResponse.json({ error: 'At least one front column is required' }, { status: 400 });
    }

    if (!body.backColumns || !Array.isArray(body.backColumns) || body.backColumns.length === 0) {
      return NextResponse.json({ error: 'At least one back column is required' }, { status: 400 });
    }

    // Generate unique deck ID
    const deckId = `deck_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Start transaction
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Insert deck
      const deckResult = await client.query(
        `INSERT INTO decks (
          id, user_id, name, description, columns, front_columns, back_columns,
          has_html
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, name, created_at`,
        [
          deckId,
          userId,
          body.name.trim(),
          body.description?.trim() || '',
          body.columns || [],
          body.frontColumns,
          body.backColumns,
          body.hasHtml || false,
        ],
      );

      // Insert deck cards
      const cardValues = body.data.map((cardData, index) => ({
        id: `card_${deckId}_${index}`,
        deckId,
        data: cardData,
      }));

      for (const card of cardValues) {
        await client.query('INSERT INTO deck_cards (id, deck_id, data) VALUES ($1, $2, $3)', [
          card.id,
          card.deckId,
          JSON.stringify(card.data),
        ]);
      }

      await client.query('COMMIT');

      const deck = deckResult.rows[0];

      return NextResponse.json({
        success: true,
        deck: {
          id: deck.id,
          name: deck.name,
          createdAt: deck.created_at,
        },
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error creating deck:', error);
    return NextResponse.json({ error: 'Failed to create deck' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;

    // Get decks with card count and view count for public decks
    const result = await pool.query(
      `SELECT
        d.id,
        d.name,
        d.description,
        d.is_public,
        d.created_at,
        COUNT(dc.id) as card_count,
        CASE
          WHEN d.is_public THEN (
            SELECT COUNT(*) FROM deck_views dv WHERE dv.deck_id = d.id
          )
          ELSE 0
        END as view_count
      FROM decks d
      LEFT JOIN deck_cards dc ON d.id = dc.deck_id
      WHERE d.user_id = $1
      GROUP BY d.id, d.name, d.description, d.is_public, d.created_at
      ORDER BY d.created_at DESC`,
      [userId],
    );

    const decks = result.rows.map((row: DeckRow) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      isPublic: row.is_public,
      cardCount: parseInt(row.card_count),
      createdAt: row.created_at,
      viewCount: row.is_public ? parseInt(row.view_count) : 0,
    }));

    return NextResponse.json({ decks });
  } catch (error) {
    console.error('Error fetching decks:', error);
    return NextResponse.json({ error: 'Failed to fetch decks' }, { status: 500 });
  }
}
