import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export default pool;

// Database schema creation helper
export async function createTablesIfNotExist() {
  try {
    // Create decks table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS decks (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        columns TEXT[] NOT NULL,
        front_columns TEXT[] NOT NULL,
        back_columns TEXT[] NOT NULL,
        has_html BOOLEAN NOT NULL DEFAULT false,
        is_randomized BOOLEAN NOT NULL DEFAULT false,
        card_limit INTEGER,
        selected_color TEXT NOT NULL DEFAULT 'blue',
        is_public BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    // Create deck_cards table
    await pool.query(`
      CREATE TABLE IF NOT EXISTS deck_cards (
        id TEXT PRIMARY KEY,
        deck_id TEXT NOT NULL REFERENCES decks(id) ON DELETE CASCADE,
        data JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(deck_id, id)
      )
    `);

    // Create study_sessions table for tracking individual study sessions
    await pool.query(`
      CREATE TABLE IF NOT EXISTS study_sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        deck_id TEXT NOT NULL REFERENCES decks(id) ON DELETE CASCADE,
        cards_studied INTEGER NOT NULL DEFAULT 0,
        duration_seconds INTEGER NOT NULL DEFAULT 0,
        session_date DATE NOT NULL DEFAULT CURRENT_DATE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    // Create user_progress table for aggregated progress metrics
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_progress (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL UNIQUE,
        total_cards_studied INTEGER NOT NULL DEFAULT 0,
        total_sessions INTEGER NOT NULL DEFAULT 0,
        current_streak_days INTEGER NOT NULL DEFAULT 0,
        longest_streak_days INTEGER NOT NULL DEFAULT 0,
        last_study_date DATE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      )
    `);

    // Create achievements table for milestones and badges
    await pool.query(`
      CREATE TABLE IF NOT EXISTS achievements (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        achievement_type TEXT NOT NULL,
        achievement_name TEXT NOT NULL,
        achievement_description TEXT NOT NULL,
        unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, achievement_type)
      )
    `);

    // Create deck_views table for tracking public deck access
    try {
      // First check if table exists
      const tableExists = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'deck_views'
        )
      `);

      if (!tableExists.rows[0].exists) {
        await pool.query(`
          CREATE TABLE deck_views (
            id TEXT PRIMARY KEY,
            deck_id TEXT NOT NULL REFERENCES decks(id) ON DELETE CASCADE,
            viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            ip_hash TEXT,
            user_agent TEXT,
            session_id TEXT
          )
        `);
      }
    } catch (error: unknown) {
      console.log('Error creating deck_views table:', error);
      // Continue anyway - the table might already exist
    }

    // Add new columns to existing decks table if they don't exist
    try {
      await pool.query(`
        ALTER TABLE decks
        ADD COLUMN IF NOT EXISTS is_public BOOLEAN NOT NULL DEFAULT false
      `);
    } catch {
      console.log('is_public column might already exist, continuing...');
    }

    // Removed share_id column logic - no longer needed

    // Create indexes for better performance
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_decks_user_id ON decks(user_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_deck_cards_deck_id ON deck_cards(deck_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_study_sessions_user_id ON study_sessions(user_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_study_sessions_deck_id ON study_sessions(deck_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_study_sessions_session_date ON study_sessions(session_date)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_user_progress_user_id ON user_progress(user_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_achievements_user_id ON achievements(user_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_deck_views_deck_id ON deck_views(deck_id)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_deck_views_viewed_at ON deck_views(viewed_at)
    `);

    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_deck_views_rate_limit ON deck_views(deck_id, ip_hash, session_id, viewed_at)
    `);
  } catch (error) {
    console.error('Error creating database tables:', error);
    throw error;
  }
}
