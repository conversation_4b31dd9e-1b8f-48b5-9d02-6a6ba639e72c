import { generateText } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Define the input schema
const ModerationInputSchema = z.object({
  name: z.string(),
  description: z.string(),
  topic: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, topic } = ModerationInputSchema.parse(body);

    // Combine all fields for analysis
    const contentToAnalyze = `
Deck Name: ${name}
Deck Description: ${description}
Deck Topic: ${topic}
    `.trim();

    console.log('Content to analyze:', contentToAnalyze);

    // Use Vercel AI SDK to analyze content
    const { text } = await generateText({
      model: 'openai/gpt-oss-120b', // Using the same model as flashcard generation
      providerOptions: {
        gateway: {
          order: ['baseten', 'fireworks'],
        },
      },
      system: `You are a content moderator AI. Analyze the provided deck information and determine if it contains any potentially harmful, inappropriate, or offensive content.
      
Guidelines:
- Flag content that contains profanity, violence, discrimination, or other harmful material
- Consider the context and intent of the content
- If the content is educational or academic in nature, be more lenient
- Return ONLY "NOT_SAFE" if the content is inappropriate
- Return ONLY "SAFE" if the content is appropriate
- Do not return any other text, just one of these two words`,
      prompt: `Analyze this deck information for harmful content:
${contentToAnalyze}`,
    });

    // Parse the response to determine if content is safe
    const isHarmful = text.trim() === 'NOT_SAFE';

    return NextResponse.json({
      isHarmful,
      reason: isHarmful ? 'Content flagged as inappropriate' : undefined,
      category: isHarmful ? 'inappropriate' : 'appropriate',
    });
  } catch (error) {
    console.error('Error during moderation:', error);
    return NextResponse.json(
      { error: 'Failed to moderate content', isHarmful: false }, // Default to not harmful on error
      { status: 500 },
    );
  }
}
