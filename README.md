# Flashcard app with TSV

_Automatically synced with your [v0.app](https://v0.app) deployments_

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/amai-studio/v0-flashcard-app-with-tsv)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.app-black?style=for-the-badge)](https://v0.app/chat/projects/lT2HqG3X98U)

## Overview

This repository will stay in sync with your deployed chats on [v0.app](https://v0.app).
Any changes you make to your deployed app will be automatically pushed to this repository from [v0.app](https://v0.app).

## Deployment

Your project is live at:

**[https://vercel.com/amai-studio/v0-flashcard-app-with-tsv](https://vercel.com/amai-studio/v0-flashcard-app-with-tsv)**

## Build your app

Continue building your app on:

**[https://v0.app/chat/projects/lT2HqG3X98U](https://v0.app/chat/projects/lT2HqG3X98U)**

## How It Works

1. Create and modify your project using [v0.app](https://v0.app)
2. Deploy your chats from the v0 interface
3. Changes are automatically pushed to this repository
4. Vercel deploys the latest version from this repository

## Environment Variables

Copy `.env.local.example` to `.env.local` and fill in the required values:

- `OPENAI_API_KEY`: Required for AI-powered flashcard generation from pasted study material
- Other variables for Auth0 and database are already configured

## Features

- **AI Flashcard Generation**: Paste study material and let AI create flashcards automatically
- **TSV Upload**: Upload tab-separated value files to create flashcards (legacy feature)
- **Auth0 Authentication**: Secure user authentication
- **Supabase Database**: Cloud database for storing decks and progress
