'use client';

import {
  ChevronLeft,
  ChevronRight,
  <PERSON><PERSON><PERSON>,
  Eye,
  EyeOff,
  Clock,
  Trophy,
  Target,
} from 'lucide-react';
import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useProgress } from '@/components/progress-provider';
import { useSessionTracker } from '@/hooks/use-session-tracker';
import { useToast } from '@/hooks/use-toast';

interface FlashcardData {
  [key: string]: string;
}

interface SessionResult {
  cardsStudied: number;
  durationSeconds: number;
  achievements?: Array<{
    name: string;
    description: string;
  }>;
}

interface FlashcardDisplayProps {
  data: FlashcardData[];
  frontColumns: string[];
  backColumns: string[];
  isRandomized: boolean;
  cardLimit: number | null;
  selectedColor: string;
  hasHtml: boolean; // Added hasHtml prop
  deckId: string;
  deckName: string;
  isLoggedIn: boolean; // Added isLoggedIn prop
  onBackToConfigure: () => void;
  onSessionComplete?: (result: SessionResult) => void;
}

export function FlashcardDisplay({
  data,
  frontColumns,
  backColumns,
  isRandomized,
  cardLimit,
  selectedColor,
  hasHtml, // Accept hasHtml prop
  deckId,
  deckName,
  isLoggedIn, // Accept isLoggedIn prop
  onBackToConfigure,
  onSessionComplete,
}: FlashcardDisplayProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [navigationDirection, setNavigationDirection] = useState<'left' | 'right' | null>(null);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [showSessionSummary, setShowSessionSummary] = useState(false);
  const flashcardContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { setActiveSession, refreshProgress } = useProgress();
  const router = useRouter();

  // Session tracking
  const {
    sessionState,
    startSession,
    endSession,
    updateProgress,
    getSessionMetrics,
    formatElapsedTime,
    isSessionActive,
  } = useSessionTracker();

  // Process the data based on configuration
  const processedData = useMemo(() => {
    let result = [...data];

    if (isRandomized) {
      result = result.sort(() => Math.random() - 0.5);
    }

    if (cardLimit && cardLimit < result.length) {
      result = result.slice(0, cardLimit);
    }

    return result;
  }, [data, isRandomized, cardLimit]);

  const currentCard = processedData[currentIndex];
  const totalCards = processedData.length;
  const progress = ((currentIndex + 1) / totalCards) * 100;

  // Initialize session when component mounts and user is logged in
  useEffect(() => {
    if (!sessionStarted && totalCards > 0 && isLoggedIn) {
      startSession({
        deckId,
        deckName,
        totalCards,
        sessionType: 'study',
      });
      setSessionStarted(true);

      // Update global active session
      setActiveSession({
        deckId,
        deckName,
        isActive: true,
        startTime: new Date(),
        currentCard: 0,
        totalCards,
        elapsedTime: 0,
        cardsCompleted: 0,
      });
    }
  }, [deckId, deckName, totalCards, sessionStarted, startSession, setActiveSession, isLoggedIn]);

  // Update session progress when card changes and user is logged in
  useEffect(() => {
    if (isSessionActive && isLoggedIn) {
      updateProgress(currentIndex, currentIndex);

      // Update global active session
      setActiveSession({
        deckId,
        deckName,
        isActive: true,
        startTime: sessionState.startTime || new Date(),
        currentCard: currentIndex,
        totalCards,
        elapsedTime: sessionState.elapsedTime,
        cardsCompleted: currentIndex,
      });
    }
  }, [
    currentIndex,
    isSessionActive,
    updateProgress,
    setActiveSession,
    sessionState.elapsedTime,
    isLoggedIn,
  ]);

  // Handle session completion
  const handleSessionComplete = useCallback(async () => {
    if (isSessionActive) {
      // Update final progress
      updateProgress(currentIndex, currentIndex + 1);

      const result = await endSession();
      if (result) {
        setShowSessionSummary(true);

        // Show achievement notifications
        if (result.achievements && result.achievements.length > 0) {
          // Emit custom event for achievements
          const achievementEvent = new CustomEvent('newAchievements', {
            detail: { achievements: result.achievements },
          });
          window.dispatchEvent(achievementEvent);

          result.achievements.forEach((achievement) => {
            toast({
              title: `🏆 Achievement Unlocked!`,
              description: `${achievement.name}: ${achievement.description}`,
              duration: 5000,
            });
          });
        }

        // Show session summary
        toast({
          title: '🎉 Study Session Complete!',
          description: `You studied ${result.cardsStudied} cards in ${formatElapsedTime(result.durationSeconds)}`,
          duration: 5000,
        });

        onSessionComplete?.(result);

        // Refresh progress data to update streak counter
        refreshProgress();

        // Clear global active session
        setActiveSession(null);

        // Redirect to profile page after a short delay to allow toast to be seen
        setTimeout(() => {
          router.push('/profile');
        }, 2000);
      }
    }
  }, [
    currentIndex,
    endSession,
    formatElapsedTime,
    isSessionActive,
    onSessionComplete,
    router,
    toast,
    updateProgress,
    setActiveSession,
    refreshProgress,
  ]);

  // Auto-complete session when all cards are studied
  useEffect(() => {
    if (currentIndex >= totalCards - 1 && isSessionActive && !showSessionSummary) {
      // Small delay to ensure the last card interaction is processed
      const timer = setTimeout(() => {
        handleSessionComplete();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [currentIndex, totalCards, isSessionActive, showSessionSummary, handleSessionComplete]);

  // Cleanup session on unmount
  useEffect(() => {
    return () => {
      if (isSessionActive) {
        setActiveSession(null);
      }
    };
  }, [isSessionActive, setActiveSession]);

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; border: string; icon: string }> = {
      blue: {
        bg: 'bg-blue-50 dark:bg-blue-950/30',
        border: 'border-blue-300 dark:border-blue-700',
        icon: 'text-blue-600/60 dark:text-blue-400/60',
      },
      green: {
        bg: 'bg-green-50 dark:bg-green-950/30',
        border: 'border-green-300 dark:border-green-700',
        icon: 'text-green-600/60 dark:text-green-400/60',
      },
      purple: {
        bg: 'bg-purple-50 dark:bg-purple-950/30',
        border: 'border-purple-300 dark:border-purple-700',
        icon: 'text-purple-600/60 dark:text-purple-400/60',
      },
      red: {
        bg: 'bg-red-50 dark:bg-red-950/30',
        border: 'border-red-300 dark:border-red-700',
        icon: 'text-red-600/60 dark:text-red-400/60',
      },
      orange: {
        bg: 'bg-orange-50 dark:bg-orange-950/30',
        border: 'border-orange-300 dark:border-orange-700',
        icon: 'text-orange-600/60 dark:text-orange-400/60',
      },
      pink: {
        bg: 'bg-pink-50 dark:bg-pink-950/30',
        border: 'border-pink-300 dark:border-pink-700',
        icon: 'text-pink-600/60 dark:text-pink-400/60',
      },
    };
    return colorMap[color] || colorMap.blue; // Default to blue if color not found
  };

  const getPulseBorderClass = (color: string) => {
    const pulseClassMap: Record<string, string> = {
      blue: 'pulse-border-blue',
      green: 'pulse-border-green',
      purple: 'pulse-border-purple',
      red: 'pulse-border-red',
      orange: 'pulse-border-orange',
      pink: 'pulse-border-pink',
    };
    return pulseClassMap[color] || pulseClassMap.blue; // Default to blue if color not found
  };

  const handleKeyPress = useCallback(
    (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          setIsNavigating(true);
          setIsFlipped(false);
          setNavigationDirection('left');
          // Circular navigation: if on first card, go to last card
          const prevIndex = currentIndex === 0 ? totalCards - 1 : currentIndex - 1;
          setCurrentIndex(prevIndex);
          setTimeout(() => {
            setIsNavigating(false);
            setNavigationDirection(null);
          }, 300);
          break;
        case 'ArrowRight':
          setIsNavigating(true);
          setIsFlipped(false);
          setNavigationDirection('right');
          // Circular navigation: if on last card, go to first card
          const nextIndex = currentIndex === totalCards - 1 ? 0 : currentIndex + 1;
          setCurrentIndex(nextIndex);
          setTimeout(() => {
            setIsNavigating(false);
            setNavigationDirection(null);
          }, 300);
          break;
        case ' ':
          e.preventDefault();
          setIsFlipped((prev) => !prev);
          break;
      }
    },
    [currentIndex, totalCards],
  );

  const handlePrevious = () => {
    setIsNavigating(true);
    setIsFlipped(false);
    setNavigationDirection('left');
    // Circular navigation: if on first card, go to last card
    const prevIndex = currentIndex === 0 ? totalCards - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    // Re-enable transitions after the animation completes
    setTimeout(() => {
      setIsNavigating(false);
      setNavigationDirection(null);
    }, 300);
  };

  const handleNext = () => {
    setIsNavigating(true);
    setIsFlipped(false);
    setNavigationDirection('right');

    // Update session progress - mark current card as completed (only if logged in)
    if (isSessionActive && isLoggedIn) {
      updateProgress(currentIndex + 1, Math.min(currentIndex + 1, totalCards));
    }

    // Circular navigation: if on last card, go to first card
    const nextIndex = currentIndex === totalCards - 1 ? 0 : currentIndex + 1;
    setCurrentIndex(nextIndex);

    // Re-enable transitions after a brief delay
    setTimeout(() => {
      setIsNavigating(false);
      setNavigationDirection(null);
    }, 300);
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleBackClick = () => {
    // When clicking on the back of the card, proceed to next card
    handleNext();
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  // Auto-scroll to flashcard when study session starts
  useEffect(() => {
    if (flashcardContainerRef.current) {
      // Small delay to ensure the component is fully rendered
      const timer = setTimeout(() => {
        flashcardContainerRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        });
      }, 300);
      return () => clearTimeout(timer);
    }
  }, []);

  const renderCardContent = (columns: string[]) => {
    const hasMultipleColumns = columns.length > 1;

    return (
      <div className="flex h-full w-full flex-col overflow-hidden">
        <div className="scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent flex-1 overflow-y-auto">
          <div
            className={`flex h-full ${hasMultipleColumns ? 'flex-col space-y-4' : 'flex-col items-center justify-center text-center'}`}
          >
            {columns.map((column, index) => {
              const content = currentCard[column] || '—';
              const isLongContent = content.length > 200;
              const isFirstColumn = index === 0;

              return (
                <div
                  key={column}
                  className={`${hasMultipleColumns ? 'space-y-2' : 'w-full'} ${!hasMultipleColumns && columns.length === 1 ? 'flex flex-1 flex-col justify-center' : ''} ${hasMultipleColumns && isFirstColumn ? 'text-center' : hasMultipleColumns ? 'text-left' : ''}`}
                >
                  {hasMultipleColumns && (
                    <h4
                      className={`text-muted-foreground dark:text-muted-foreground/80 border-border/50 dark:border-border flex-shrink-0 pb-1 text-sm font-semibold tracking-wide uppercase ${isFirstColumn ? 'text-center' : ''}`}
                    >
                      {column}
                    </h4>
                  )}
                  <div
                    className={`relative ${!hasMultipleColumns ? 'flex flex-1 items-center justify-center' : isFirstColumn ? 'flex items-center justify-center' : ''}`}
                  >
                    {hasHtml ? (
                      <div
                        className={`${
                          hasMultipleColumns
                            ? isFirstColumn
                              ? isLongContent
                                ? 'text-center text-xl leading-relaxed'
                                : 'text-center text-4xl md:text-7xl leading-relaxed'
                              : isLongContent
                                ? 'text-left text-base leading-relaxed'
                                : 'text-left text-lg leading-relaxed'
                            : isLongContent
                              ? 'text-xl leading-relaxed'
                              : 'text-4xl md:text-7xl leading-relaxed'
                        } text-foreground w-full text-pretty break-words select-none ${isLongContent && hasMultipleColumns ? 'scrollbar-thin scrollbar-thumb-muted/50 max-h-50 overflow-y-auto pr-2' : 'pr-2'}`}
                        dangerouslySetInnerHTML={{
                          __html: content,
                        }}
                      />
                    ) : (
                      <div
                        className={`${
                          hasMultipleColumns
                            ? isFirstColumn
                              ? isLongContent
                                ? 'text-center text-xl leading-relaxed'
                                : 'text-center text-4xl md:text-7xl leading-relaxed'
                              : isLongContent
                                ? 'text-left text-base leading-relaxed'
                                : 'text-left text-lg leading-relaxed'
                            : isLongContent
                              ? 'text-xl leading-relaxed'
                              : 'text-4xl md:text-7xl leading-relaxed'
                        } text-foreground w-full text-pretty break-words whitespace-pre-wrap select-none ${isLongContent && hasMultipleColumns ? 'scrollbar-thin scrollbar-thumb-muted/50 max-h-50 overflow-y-auto pr-2' : 'pr-2'}`}
                      >
                        {content}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  if (totalCards === 0) {
    return (
      <Card className="mx-auto max-w-2xl">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <p className="text-muted-foreground mb-4">No cards available to study</p>
          <Button onClick={onBackToConfigure} variant="outline" className="select-none">
            Back to Configuration
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="mx-auto max-w-4xl space-y-4 sm:space-y-6">
      {/* Header with Progress */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={onBackToConfigure}
            size="sm"
            className="bg-transparent select-none"
          >
            <Settings className="mr-2 h-4 w-4" />
            Configure
          </Button>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-accent/10 text-accent border-accent/20">
              {currentIndex + 1} of {totalCards}
            </Badge>
            {isRandomized && (
              <Badge variant="secondary" className="text-xs">
                Randomized
              </Badge>
            )}
          </div>
        </div>

        {/* Session Metrics - only for logged in users */}
        {isSessionActive && isLoggedIn && (
          <div className="flex flex-wrap items-center gap-2 text-sm">
            <div className="flex items-center gap-1 bg-blue-50 dark:bg-blue-950/30 px-2 py-1 rounded-md">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="font-mono text-blue-700 dark:text-blue-300">
                {formatElapsedTime(sessionState.elapsedTime)}
              </span>
            </div>
            <div className="flex items-center gap-1 bg-green-50 dark:bg-green-950/30 px-2 py-1 rounded-md">
              <Target className="h-4 w-4 text-green-600" />
              <span className="text-green-700 dark:text-green-300">
                {sessionState.cardsCompleted}/{totalCards}
              </span>
            </div>
            {sessionState.cardsCompleted > 0 && (
              <div className="flex items-center gap-1 bg-purple-50 dark:bg-purple-950/30 px-2 py-1 rounded-md">
                <Trophy className="h-4 w-4 text-purple-600" />
                <span className="text-purple-700 dark:text-purple-300 text-xs">
                  {Math.round(getSessionMetrics().averageTimePerCard)}s/card
                </span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress value={progress} className="progress-transition h-2 w-full" />
      </div>

      {/* Flashcard */}
      <div ref={flashcardContainerRef} className="relative">
        <div
          key={currentIndex} // Force re-render when card changes
          className={`card-transition relative w-full ${
            isNavigating
              ? `card-transitioning overflow-hidden ${navigationDirection === 'right' ? 'card-exit-left' : navigationDirection === 'left' ? 'card-exit-right' : ''}`
              : ''
          }`}
          style={{
            height: 'clamp(300px, 60vh, 600px)',
          }}
        >
          {/* Front Side */}
          <Card
            key={`front-${currentIndex}`}
            className={`${getColorClasses(selectedColor).bg} ${getColorClasses(selectedColor).border} ${getPulseBorderClass(selectedColor)} absolute inset-0 h-full w-full border-2 shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl ${
              isFlipped
                ? 'pointer-events-none scale-95 opacity-0'
                : `scale-100 cursor-pointer opacity-100 ${isNavigating ? (navigationDirection === 'right' ? 'card-enter-right' : navigationDirection === 'left' ? 'card-enter-left' : '') : ''}`
            }`}
            onClick={handleFlip}
          >
            <CardContent className="flex h-full flex-col p-6">
              <div className="min-h-0 w-full flex-1">{renderCardContent(frontColumns)}</div>
              <div className="absolute right-4 bottom-4">
                <Eye className={`${getColorClasses(selectedColor).icon} h-5 w-5`} />
              </div>
            </CardContent>
          </Card>

          {/* Back Side */}
          <Card
            key={`back-${currentIndex}`}
            className={`absolute inset-0 h-full w-full rounded-xl border-2 border-dashed ${getColorClasses(selectedColor).border} ${getColorClasses(selectedColor).bg} ${getPulseBorderClass(selectedColor)} shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl ${
              isFlipped
                ? `scale-100 cursor-pointer opacity-100 ${isNavigating ? (navigationDirection === 'right' ? 'card-enter-right' : navigationDirection === 'left' ? 'card-enter-left' : '') : ''}`
                : 'pointer-events-none scale-95 opacity-0'
            }`}
            onClick={handleBackClick}
          >
            <CardContent className="flex h-full flex-col p-6">
              <div className="min-h-0 w-full flex-1">{renderCardContent(backColumns)}</div>
              <div className="absolute right-4 bottom-4">
                <EyeOff className={`${getColorClasses(selectedColor).icon} h-5 w-5`} />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="flex flex-wrap items-center justify-center gap-4">
        <Button variant="outline" onClick={handlePrevious} className="bg-transparent select-none">
          <ChevronLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        <div className="bg-muted/30 flex items-center gap-2 rounded-lg px-3 py-2 sm:px-4">
          <span className="text-sm sm:text-base font-medium">{currentIndex + 1}</span>
          <span className="text-muted-foreground text-sm sm:text-base">of</span>
          <span className="text-sm sm:text-base font-medium">{totalCards}</span>
        </div>

        <Button variant="outline" onClick={handleNext} className="bg-transparent select-none">
          Next
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>

        {/* End Session Button - only for logged in users */}
        {isSessionActive && isLoggedIn && (
          <Button
            variant="default"
            onClick={handleSessionComplete}
            className="bg-green-600 hover:bg-green-700 text-white select-none w-full sm:w-auto"
          >
            <Trophy className="mr-2 h-4 w-4" />
            Finish Session
          </Button>
        )}
      </div>

      {/* Study Tips */}
      <div className="text-muted-foreground bg-muted/20 rounded-lg p-4 text-center text-base">
        <p>
          💡 <strong>Study Tips:</strong> Take your time to understand each concept
        </p>
      </div>
    </div>
  );
}
