'use client';

import { useUser } from '@auth0/nextjs-auth0';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { Suspense } from 'react';

import { AuthButtons } from '@/components/auth-buttons';
import { ThemeToggleSwitch } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';
import { Toaster } from '@/components/ui/toaster';

interface ClientLayoutContentProps {
  children: React.ReactNode;
}

export function ClientLayoutContent({ children }: ClientLayoutContentProps) {
  const pathname = usePathname();
  const { user } = useUser();
  const showBackToHome =
    pathname === '/terms-of-service' ||
    pathname === '/privacy-policy' ||
    pathname === '/profile' ||
    pathname?.startsWith('/study');
  const isCreateDeck = pathname === '/create-deck';
  const showBackToProfile = pathname?.startsWith('/study') || isCreateDeck;

  return (
    <>
      {/* Header container */}
      <header className="bg-background border-border sticky top-0 z-50 w-full border-b shadow-sm">
        <div className="mx-auto flex max-w-4xl items-center justify-between px-4 py-3">
          {/* Left section - Navigation Buttons */}
          <div className="flex items-center gap-2">
            {showBackToHome && !(user && showBackToProfile) && (
              <Link href="/">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4" />
                  <span className="sm:inline">Home</span>
                </Button>
              </Link>
            )}
            {showBackToProfile && user && (
              <Link href="/profile">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4" />
                  <span className="sm:inline">Profile</span>
                </Button>
              </Link>
            )}
          </div>

          {/* Right section - Auth and Theme Toggle */}
          <div className="flex items-center gap-2">
            <AuthButtons />
            <ThemeToggleSwitch />
          </div>
        </div>
      </header>

      <div className="flex-grow">
        <Suspense fallback={null}>{children}</Suspense>
      </div>

      {/* Toast notifications */}
      <Toaster />
    </>
  );
}
