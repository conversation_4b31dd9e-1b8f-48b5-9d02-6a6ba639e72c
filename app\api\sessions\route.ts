import { NextRequest, NextResponse } from 'next/server';
import { PoolClient } from 'pg';

import { auth0 } from '@/lib/auth0';
import pool from '@/lib/db';

interface CreateSessionRequest {
  deckId: string;
  cardsStudied: number;
  durationSeconds: number;
}

interface Achievement {
  type: string;
  name: string;
  description: string;
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;
    const body: CreateSessionRequest = await request.json();

    // Validate required fields
    if (!body.deckId) {
      return NextResponse.json({ error: 'Deck ID is required' }, { status: 400 });
    }

    if (body.cardsStudied < 0) {
      return NextResponse.json({ error: 'Cards studied cannot be negative' }, { status: 400 });
    }

    if (body.durationSeconds < 0) {
      return NextResponse.json({ error: 'Duration cannot be negative' }, { status: 400 });
    }

    // Verify deck exists and is accessible (can be public or owned by user)
    const deckCheck = await pool.query('SELECT id, is_public, user_id FROM decks WHERE id = $1', [
      body.deckId,
    ]);

    if (deckCheck.rows.length === 0) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    const deck = deckCheck.rows[0];
    // Allow access if deck is public OR if user owns the deck
    if (!deck.is_public && deck.user_id !== userId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const sessionDate = new Date().toISOString().split('T')[0];

      // Insert study session
      await client.query(
        `INSERT INTO study_sessions (
          id, user_id, deck_id, cards_studied, duration_seconds, session_date
        ) VALUES ($1, $2, $3, $4, $5, $6)`,
        [sessionId, userId, body.deckId, body.cardsStudied, body.durationSeconds, sessionDate],
      );

      // Get or create user progress record
      const progressResult = await client.query('SELECT * FROM user_progress WHERE user_id = $1', [
        userId,
      ]);

      if (progressResult.rows.length > 0) {
        // Update progress
        await client.query(
          `UPDATE user_progress
           SET total_cards_studied = total_cards_studied + $1,
               total_sessions = total_sessions + 1,
               last_study_date = $2,
               updated_at = NOW()
           WHERE user_id = $3`,
          [body.cardsStudied, sessionDate, userId],
        );
      } else {
        // Create new progress record
        const progressId = `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await client.query(
          `INSERT INTO user_progress (
            id, user_id, total_cards_studied, total_sessions, last_study_date
          ) VALUES ($1, $2, $3, $4, $5)`,
          [progressId, userId, body.cardsStudied, 1, sessionDate],
        );
      }

      // Check for achievements
      const achievements = await checkAndAwardAchievements(client, userId, body.cardsStudied);

      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        session: {
          id: sessionId,
          deckId: body.deckId,
          cardsStudied: body.cardsStudied,
          durationSeconds: body.durationSeconds,
          sessionDate,
        },
        achievements: achievements.length > 0 ? achievements : undefined,
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error recording study session:', error);
    return NextResponse.json({ error: 'Failed to record study session' }, { status: 500 });
  }
}

async function checkAndAwardAchievements(client: PoolClient, userId: string, cardsStudied: number) {
  const achievements: Achievement[] = [];

  // Get current progress
  const progressResult = await client.query('SELECT * FROM user_progress WHERE user_id = $1', [
    userId,
  ]);

  if (progressResult.rows.length === 0) return achievements;

  const progress = progressResult.rows[0];
  const totalCards = progress.total_cards_studied + cardsStudied;
  const totalSessions = progress.total_sessions + 1;

  // Achievement definitions
  const achievementChecks = [
    {
      type: 'first_session',
      name: 'First Steps',
      description: 'Completed your first study session',
      condition: totalSessions >= 1,
    },
    {
      type: 'ten_sessions',
      name: 'Getting Started',
      description: 'Completed 10 study sessions',
      condition: totalSessions >= 10,
    },
    {
      type: 'fifty_sessions',
      name: 'Dedicated Learner',
      description: 'Completed 50 study sessions',
      condition: totalSessions >= 50,
    },
    {
      type: 'hundred_cards',
      name: 'Card Collector',
      description: 'Studied 100 cards',
      condition: totalCards >= 100,
    },
    {
      type: 'five_hundred_cards',
      name: 'Knowledge Seeker',
      description: 'Studied 500 cards',
      condition: totalCards >= 500,
    },
    {
      type: 'thousand_cards',
      name: 'Scholar',
      description: 'Studied 1000 cards',
      condition: totalCards >= 1000,
    },
  ];

  for (const achievement of achievementChecks) {
    // Check if achievement already exists
    const existing = await client.query(
      'SELECT id FROM achievements WHERE user_id = $1 AND achievement_type = $2',
      [userId, achievement.type],
    );

    if (existing.rows.length === 0 && achievement.condition) {
      const achievementId = `achievement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      await client.query(
        `INSERT INTO achievements (
          id, user_id, achievement_type, achievement_name, achievement_description
        ) VALUES ($1, $2, $3, $4, $5)`,
        [achievementId, userId, achievement.type, achievement.name, achievement.description],
      );
      achievements.push(achievement);
    }
  }

  return achievements;
}
