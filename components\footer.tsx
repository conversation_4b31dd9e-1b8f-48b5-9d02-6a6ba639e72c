import Link from 'next/link';
import React from 'react';

export function Footer() {
  return (
    <footer className="py-4">
      <div className="container mx-auto max-w-4xl px-4 text-center">
        <div className="flex flex-col items-center gap-2 sm:flex-row sm:justify-center sm:gap-4">
          <p className="text-muted-foreground text-xs">© {new Date().getFullYear()} Amasugi</p>
          <div className="flex gap-4 text-xs">
            <Link
              href="/terms-of-service"
              className="text-muted-foreground hover:text-foreground underline transition-colors"
            >
              Terms of Service
            </Link>
            <Link
              href="/privacy-policy"
              className="text-muted-foreground hover:text-foreground underline transition-colors"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
