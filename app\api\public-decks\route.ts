import { NextRequest, NextResponse } from 'next/server';

import pool, { createTablesIfNotExist } from '@/lib/db';

interface PublicDeckRow {
  id: string;
  name: string;
  description: string;
  created_at: string;
  card_count: string;
  view_count: string;
}

export async function GET(request: NextRequest) {
  try {
    // Ensure database tables exist
    await createTablesIfNotExist();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const search = searchParams.get('search') || '';
    const sort = searchParams.get('sort') || 'view_count';

    // Validate pagination parameters
    const validPage = Math.max(1, page);
    const validLimit = Math.min(50, Math.max(1, limit)); // Max 50 per page
    const offset = (validPage - 1) * validLimit;

    // Add sorting
    let orderBy = '';
    switch (sort) {
      case 'view_count':
        orderBy = 'view_count DESC, d.created_at DESC';
        break;
      case 'created_at':
        orderBy = 'd.created_at DESC';
        break;
      case 'name':
        orderBy = 'd.name ASC';
        break;
      case 'card_count':
        orderBy = 'card_count DESC';
        break;
      default:
        orderBy = 'view_count DESC, d.created_at DESC';
    }

    let totalCount = 0;
    let decksQuery = '';
    let params: (string | number)[] = [];

    if (search.trim()) {
      // Query with search
      const countQuery = `
        SELECT COUNT(DISTINCT d.id) as total
        FROM decks d
        WHERE d.is_public = true AND (d.name ILIKE $1 OR d.description ILIKE $1)
      `;
      const countResult = await pool.query(countQuery, [`%${search.trim()}%`]);
      totalCount = parseInt(countResult.rows[0]?.total || '0');

      decksQuery = `
        SELECT
          d.id,
          d.name,
          d.description,
          d.created_at,
          COUNT(dc.id) as card_count,
          CASE
            WHEN d.is_public THEN (
              SELECT COUNT(*) FROM deck_views dv WHERE dv.deck_id = d.id
            )
            ELSE 0
          END as view_count
        FROM decks d
        LEFT JOIN deck_cards dc ON d.id = dc.deck_id
        WHERE d.is_public = true AND (d.name ILIKE $1 OR d.description ILIKE $1)
        GROUP BY d.id, d.name, d.description, d.created_at
        ORDER BY ${orderBy}
        LIMIT $2 OFFSET $3
      `;
      params = [`%${search.trim()}%`, validLimit, offset];
    } else {
      // Query without search
      const countQuery = `
        SELECT COUNT(DISTINCT d.id) as total
        FROM decks d
        WHERE d.is_public = true
      `;
      const countResult = await pool.query(countQuery);
      totalCount = parseInt(countResult.rows[0]?.total || '0');

      decksQuery = `
        SELECT
          d.id,
          d.name,
          d.description,
          d.created_at,
          COUNT(dc.id) as card_count,
          CASE
            WHEN d.is_public THEN (
              SELECT COUNT(*) FROM deck_views dv WHERE dv.deck_id = d.id
            )
            ELSE 0
          END as view_count
        FROM decks d
        LEFT JOIN deck_cards dc ON d.id = dc.deck_id
        WHERE d.is_public = true
        GROUP BY d.id, d.name, d.description, d.created_at
        ORDER BY ${orderBy}
        LIMIT $1 OFFSET $2
      `;
      params = [validLimit, offset];
    }

    const result = await pool.query(decksQuery, params);

    const decks = result.rows.map((row: PublicDeckRow) => ({
      id: row.id,
      name: row.name,
      description: row.description,
      cardCount: parseInt(row.card_count),
      createdAt: row.created_at,
      viewCount: parseInt(row.view_count),
    }));

    const totalPages = Math.ceil(totalCount / validLimit);

    return NextResponse.json({
      decks,
      pagination: {
        currentPage: validPage,
        totalPages,
        totalCount,
        hasNextPage: validPage < totalPages,
        hasPrevPage: validPage > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching public decks:', error);
    return NextResponse.json({ error: 'Failed to fetch public decks' }, { status: 500 });
  }
}
